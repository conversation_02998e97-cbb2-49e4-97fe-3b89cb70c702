<x-filament-panels::page>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Sidebar: Daftar Kontak -->
        <div class="col-span-1 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold">Kontak</h2>
                @if($unreadCount > 0)
                    <span class="px-2 py-1 bg-primary-500 text-white rounded-full text-xs">
                        {{ $unreadCount }} baru
                    </span>
                @endif
            </div>
            
            <div class="space-y-2 max-h-[500px] overflow-y-auto">
                @if($messages->isEmpty())
                    <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                        Belum ada percakapan
                    </div>
                @else
                    @php
                        $uniqueUsers = collect();
                        foreach ($messages as $message) {
                            $otherUser = $message->sender_id == auth()->id() ? $message->receiver : $message->sender;
                            if (!$uniqueUsers->contains('id', $otherUser->id)) {
                                $uniqueUsers->push($otherUser);
                            }
                        }
                    @endphp
                    
                    @foreach($uniqueUsers as $user)
                        @php
                            $latestMessage = $messages->first(function ($message) use ($user) {
                                return ($message->sender_id == $user->id && $message->receiver_id == auth()->id()) || 
                                       ($message->sender_id == auth()->id() && $message->receiver_id == $user->id);
                            });
                            
                            $unreadMessages = \App\Models\Chat::where('sender_id', $user->id)
                                ->where('receiver_id', auth()->id())
                                ->where('is_read', false)
                                ->count();
                        @endphp
                        
                        <button wire:click="selectUser({{ $user->id }})" 
                                class="w-full text-left p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                                       {{ $selectedUser == $user->id ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">{{ $user->name }}</span>
                                @if($unreadMessages > 0)
                                    <span class="px-2 py-0.5 bg-primary-500 text-white rounded-full text-xs">
                                        {{ $unreadMessages }}
                                    </span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                                {{ Str::limit($latestMessage->message, 40) }}
                            </div>
                            <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                {{ $latestMessage->created_at->format('d M Y H:i') }}
                            </div>
                        </button>
                    @endforeach
                @endif
            </div>
        </div>
        
        <!-- Main Chat Area -->
        <div class="col-span-1 md:col-span-2">
            @if($selectedUser)
                @php
                    $chatUser = \App\Models\User::find($selectedUser);
                @endphp
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow h-full flex flex-col">
                    <!-- Chat Header -->
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium">{{ $chatUser->name }}</h3>
                    </div>
                    
                    <!-- Chat Messages -->
                    <div class="flex-1 p-4 overflow-y-auto max-h-[400px] space-y-4" id="chat-messages">
                        @foreach($messages as $message)
                            <div class="flex {{ $message->sender_id == auth()->id() ? 'justify-end' : 'justify-start' }}">
                                <div class="max-w-[80%] {{ $message->sender_id == auth()->id() 
                                    ? 'bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-100' 
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' }} 
                                    rounded-lg p-3 shadow-sm">
                                    
                                    @if($message->pengajuan)
                                        <div class="text-xs bg-gray-200 dark:bg-gray-600 p-1 rounded mb-2">
                                            Terkait Pengajuan: ID {{ $message->pengajuan->id }} - {{ $message->pengajuan->kotatujuan }}
                                        </div>
                                    @endif
                                    
                                    <div class="whitespace-pre-wrap">{{ $message->message }}</div>
                                    
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex justify-between items-center">
                                        <span>{{ $message->created_at->format('d M Y H:i') }}</span>
                                        
                                        @if($message->sender_id == auth()->id())
                                            <span>
                                                @if($message->is_read)
                                                    <x-heroicon-s-check class="w-4 h-4 text-green-500 inline" />
                                                @else
                                                    <x-heroicon-s-check class="w-4 h-4 text-gray-400 inline" />
                                                @endif
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Chat Input -->
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                        <form wire:submit="sendMessage">
                            {{ $this->form }}
                            
                            <div class="mt-3 flex justify-end">
                                <x-filament::button type="submit">
                                    Kirim
                                </x-filament::button>
                            </div>
                        </form>
                    </div>
                </div>
            @else
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 h-full flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-gray-400 dark:text-gray-500 mb-4">
                            <x-heroicon-o-chat-bubble-left-right class="w-16 h-16 mx-auto" />
                        </div>
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pilih kontak untuk memulai percakapan
                        </h3>
                        <p class="text-gray-500 dark:text-gray-400">
                            Atau gunakan form di bawah untuk memulai percakapan baru
                        </p>
                        
                        <div class="mt-6 max-w-md mx-auto">
                            <form wire:submit="sendMessage">
                                {{ $this->form }}
                                
                                <div class="mt-3 flex justify-end">
                                    <x-filament::button type="submit">
                                        Kirim
                                    </x-filament::button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    
    <script>
        document.addEventListener('livewire:initialized', () => {
            const scrollToBottom = () => {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            };
            
            // Scroll to bottom on initial load
            scrollToBottom();
            
            // Scroll to bottom when messages are updated
            Livewire.hook('morph.updated', ({ el }) => {
                if (el.id === 'chat-messages') {
                    scrollToBottom();
                }
            });
            
            // Set up polling to refresh messages
            setInterval(() => {
                @this.dispatch('refresh-messages');
            }, 10000); // Refresh every 10 seconds
        });
    </script>
</x-filament-panels::page>
