<x-filament-panels::page>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="border-b border-gray-200 dark:border-gray-700 p-4 text-center">
            <h2 class="text-lg font-semibold">Messages</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4">
            <!-- Sidebar: Daftar Kontak -->
            <div class="col-span-1 border-r border-gray-200 dark:border-gray-700">
                <div class="p-3">
                    <input type="text" placeholder="Cari user" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm" wire:model.live.debounce.300ms="searchTerm">
                </div>

                <div class="overflow-y-auto max-h-[500px] divide-y divide-gray-200 dark:divide-gray-700">
                    @if($messages->isEmpty())
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            Belum ada percakapan
                        </div>
                    @else
                        @php
                            $uniqueUsers = collect();
                            foreach ($messages as $message) {
                                $otherUser = $message->sender_id == auth()->id() ? $message->receiver : $message->sender;
                                if (!$uniqueUsers->contains('id', $otherUser->id)) {
                                    $uniqueUsers->push($otherUser);
                                }
                            }
                        @endphp

                        @foreach($uniqueUsers as $user)
                            @php
                                $latestMessage = $messages->first(function ($message) use ($user) {
                                    return ($message->sender_id == $user->id && $message->receiver_id == auth()->id()) ||
                                           ($message->sender_id == auth()->id() && $message->receiver_id == $user->id);
                                });

                                $unreadMessages = \App\Models\Chat::where('sender_id', $user->id)
                                    ->where('receiver_id', auth()->id())
                                    ->where('is_read', false)
                                    ->count();

                                $userImage = $user->pegawai && $user->pegawai->foto ? asset('storage/' . $user->pegawai->foto) : asset('storage/profile-photos/user.jpg');
                            @endphp

                            <button wire:click="selectUser({{ $user->id }})"
                                    class="w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
                                           {{ $selectedUser == $user->id ? 'bg-gray-50 dark:bg-gray-700' : '' }}">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <img src="{{ $userImage }}" alt="{{ $user->name }}" class="w-10 h-10 rounded-full object-cover">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex justify-between items-baseline">
                                            <p class="font-medium text-sm truncate">{{ $user->name }}</p>
                                            @if($unreadMessages > 0)
                                                <span class="px-1.5 py-0.5 bg-primary-500 text-white rounded-full text-xs">
                                                    {{ $unreadMessages }}
                                                </span>
                                            @endif
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                            @if($latestMessage->sender_id == auth()->id())
                                                <span class="text-gray-400">Anda: </span>
                                            @endif
                                            {{ Str::limit($latestMessage->message, 30) }}
                                        </p>
                                    </div>
                                </div>
                            </button>
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="col-span-1 md:col-span-3 flex flex-col h-[600px]">
                @if($selectedUser)
                    @php
                        $chatUser = \App\Models\User::find($selectedUser);
                        $userImage = $chatUser->pegawai && $chatUser->pegawai->foto ? asset('storage/' . $chatUser->pegawai->foto) : asset('storage/profile-photos/user.jpg');
                    @endphp

                    <!-- Chat Header -->
                    <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <img src="{{ $userImage }}" alt="{{ $chatUser->name }}" class="w-10 h-10 rounded-full object-cover">
                        </div>
                        <div>
                            <h3 class="font-medium">{{ $chatUser->name }}</h3>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="flex-1 p-4 overflow-y-auto space-y-4" id="chat-messages">
                        @php
                            $currentDate = null;
                            $groupedMessages = $messages->groupBy(function($message) {
                                return $message->created_at->format('Y-m-d');
                            });
                        @endphp

                        @foreach($groupedMessages as $date => $dateMessages)
                            <div class="text-center my-2">
                                <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 px-2 py-1 rounded-full">
                                    {{ \Carbon\Carbon::parse($date)->format('d/m/Y') }}
                                </span>
                            </div>

                            @foreach($dateMessages as $message)
                                <div class="flex {{ $message->sender_id == auth()->id() ? 'justify-end' : 'justify-start' }}">
                                    @if($message->sender_id != auth()->id())
                                        <div class="flex-shrink-0 mr-2">
                                            <img src="{{ $userImage }}" alt="{{ $chatUser->name }}" class="w-8 h-8 rounded-full object-cover">
                                        </div>
                                    @endif

                                    <div class="max-w-[70%] {{ $message->sender_id == auth()->id()
                                        ? 'bg-primary-100 dark:bg-primary-700 text-primary-800 dark:text-primary-100'
                                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' }}
                                        rounded-lg p-3">

                                        @if($message->pengajuan)
                                            <div class="text-xs bg-gray-200 dark:bg-gray-600 p-1 rounded mb-2">
                                                Terkait Pengajuan: ID {{ $message->pengajuan->id }} - {{ $message->pengajuan->kotatujuan }}
                                            </div>
                                        @endif

                                        <div class="whitespace-pre-wrap">{{ $message->message }}</div>

                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                                            {{ $message->created_at->format('H:i') }}

                                            @if($message->sender_id == auth()->id())
                                                <span class="ml-1">
                                                    @if($message->is_read)
                                                        <x-heroicon-s-check class="w-4 h-4 text-green-500 inline" />
                                                    @else
                                                        <x-heroicon-s-check class="w-4 h-4 text-gray-400 inline" />
                                                    @endif
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endforeach
                    </div>

                    <!-- Chat Input -->
                    <div class="p-3 border-t border-gray-200 dark:border-gray-700">
                        <form wire:submit="sendMessage" class="flex items-end space-x-2">
                            <div class="flex-1">
                                @if($this->form->getComponents()[2])
                                    {{ $this->form->getComponents()[2] }}
                                @endif
                            </div>
                            <div>
                                <x-filament::button type="submit" size="sm">
                                    <x-heroicon-s-paper-airplane class="w-4 h-4 -rotate-45" />
                                </x-filament::button>
                            </div>
                        </form>
                    </div>
                @else
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="text-center">
                            <div class="text-gray-400 dark:text-gray-500 mb-4">
                                <x-heroicon-o-chat-bubble-left-right class="w-16 h-16 mx-auto" />
                            </div>
                            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Pilih kontak untuk memulai percakapan
                            </h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                Atau gunakan form di bawah untuk memulai percakapan baru
                            </p>

                            <div class="mt-6 max-w-md mx-auto">
                                <form wire:submit="sendMessage">
                                    {{ $this->form }}

                                    <div class="mt-3 flex justify-end">
                                        <x-filament::button type="submit">
                                            Kirim
                                        </x-filament::button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', () => {
            const scrollToBottom = () => {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            };

            // Scroll to bottom on initial load
            scrollToBottom();

            // Scroll to bottom when messages are updated
            Livewire.hook('morph.updated', ({ el }) => {
                if (el.id === 'chat-messages') {
                    scrollToBottom();
                }
            });

            // Set up polling to refresh messages
            setInterval(() => {
                @this.dispatch('refresh-messages');
            }, 10000); // Refresh every 10 seconds
        });
    </script>
</x-filament-panels::page>
