<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #000;
        }
        th, td {
            padding: 5px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
        }
        .footer p {
            margin: 5px 0;
        }
        .print-button {
            text-align: center;
            margin: 20px 0;
        }
        .print-button button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .print-button button:hover {
            background-color: #45a049;
        }
        @media print {
            .print-button {
                display: none;
            }
            body {
                padding: 0;
                margin: 0;
            }
            @page {
                size: landscape;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN PERJALANAN DINAS</h1>
        <p>PT PLN (PERSERO)</p>
        <p>Tanggal Cetak: {{ $tanggalCetak }}</p>
    </div>

    <div class="print-button">
        <button onclick="window.print()">Cetak Laporan</button>
    </div>

    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>PERNR</th>
                <th>NAMA</th>
                <th>NIP</th>
                <th>GRADE</th>
                <th>JABATAN</th>
                <th>KOTA TUJUAN</th>
                <th>TANGGAL BERANGKAT</th>
                <th>TANGGAL KEMBALI</th>
                <th>JENIS PERJALANAN</th>
                <th>TUJUAN PERJALANAN</th>
                <th>JENIS TRANSPORTASI</th>
                <th>KETERANGAN</th>
                <th>NILAI TIKET</th>
                <th>NILAI HOTEL</th>
                <th>STATUS</th>
            </tr>
        </thead>
        <tbody>
            @forelse($data as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $item->pegawai->pernr ?? '-' }}</td>
                    <td>{{ $item->pegawai->nama ?? '-' }}</td>
                    <td>{{ $item->pegawai->nip ?? '-' }}</td>
                    <td>{{ $item->pegawai->grade->namagrade ?? '-' }}</td>
                    <td>{{ $item->pegawai->jabatan->nama_jabatan ?? '-' }}</td>
                    <td>{{ $item->kotatujuan ?? '-' }}</td>
                    <td>{{ $item->tanggalberangkat ? date('d/m/Y', strtotime($item->tanggalberangkat)) : '-' }}</td>
                    <td>{{ $item->tanggalkembali ? date('d/m/Y', strtotime($item->tanggalkembali)) : '-' }}</td>
                    <td>{{ $item->jenisperjalanan ?? '-' }}</td>
                    <td>{{ $item->tujuanperjalanan ?? '-' }}</td>
                    <td>{{ $item->jenistransport ?? '-' }}</td>
                    <td>{{ $item->keteranganperjalanan ?? '-' }}</td>
                    <td>{{ $item->totaluang ? 'Rp ' . number_format($item->totaluang->nilaitiket, 0, ',', '.') : 'Rp 0' }}</td>
                    <td>{{ $item->hotel ? 'Rp ' . number_format($item->hotel, 0, ',', '.') : 'Rp 0' }}</td>
                    <td>{{ $item->status ?? '-' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="16" style="text-align: center;">Tidak ada data</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>Jakarta, {{ date('d F Y') }}</p>
        <br><br><br>
        <p>{{ $user->name ?? 'Admin' }}</p>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Uncomment line below to automatically print when page loads
            // window.print();
        };
    </script>
</body>
</html>
