<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarjabatanResource\Pages;
use App\Filament\Resources\DaftarjabatanResource\RelationManagers;
use App\Models\Daftarjabatan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Contracts\HasTable;
use stdClass;

class DaftarjabatanResource extends Resource
{
    protected static ?string $model = Daftarjabatan::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $label = 'Daftar Jabatan';
    protected static ?string $pluralLabel = 'Daftar Jabatan';
    protected static ?string $slug = 'daftar-jabatan';
    protected static ?string $navigationLabel = 'Daftar Jabatan';
    protected static ?string $recordTitleAttribute = 'nama_jabatan';
    protected static ?string $modelLabel = 'Daftar Jabatan';
    protected static ?string $pluralModelLabel = 'Daftar Jabatan';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Fieldset::make('Daftar Jabatan')
                    ->schema([
                        Forms\Components\TextInput::make('nama_jabatan')
                            ->required()
                            ->label('Nama Jabatan'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('No.')->state(
                    static function (HasTable $livewire, stdClass $rowLoop): string {
                        return (string) (
                            $rowLoop->iteration +
                            ($livewire->getTableRecordsPerPage() * (
                                $livewire->getTablePage() - 1
                            ))
                        );
                    }
                ),
                Tables\Columns\TextColumn::make('nama_jabatan')->label('Nama Jabatan')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDaftarjabatans::route('/'),
        ];
    }
}
