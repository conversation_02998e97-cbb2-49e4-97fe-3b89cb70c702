<div>
    <x-filament::modal
        :heading="__('Ganti Password')"
        :description="__('Pastikan akun Anda menggunakan password yang kuat untuk keamanan.')"
        icon="heroicon-o-key"
        icon-color="primary"
        width="md"
        wire:model.defer="isOpen"
    >
        <div class="space-y-4">
            <x-filament::input.wrapper
                :label="__('Password Saat Ini')"
                required
            >
                <x-filament::input
                    type="password"
                    wire:model="current_password"
                    required
                    autocomplete="current-password"
                    placeholder="Masukkan password saat ini"
                />
            </x-filament::input.wrapper>

            <x-filament::input.wrapper
                :label="__('Password Baru')"
                required
            >
                <x-filament::input
                    type="password"
                    wire:model="password"
                    required
                    autocomplete="new-password"
                    placeholder="Masukkan password baru"
                />
            </x-filament::input.wrapper>

            <x-filament::input.wrapper
                :label="__('Konfirmasi Password Baru')"
                required
            >
                <x-filament::input
                    type="password"
                    wire:model="password_confirmation"
                    required
                    autocomplete="new-password"
                    placeholder="Konfirmasi password baru"
                />
            </x-filament::input.wrapper>
        </div>

        <x-slot name="footerActions">
            <x-filament::button
                color="gray"
                wire:click="close"
            >
                {{ __('Batal') }}
            </x-filament::button>

            <x-filament::button
                wire:click="updatePassword"
                wire:loading.attr="disabled"
            >
                {{ __('Simpan') }}
            </x-filament::button>
        </x-slot>
    </x-filament::modal>
</div>
