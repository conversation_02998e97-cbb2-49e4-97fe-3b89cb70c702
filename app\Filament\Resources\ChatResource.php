<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChatResource\Pages;
use App\Models\Chat;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ChatResource extends Resource
{
    protected static ?string $model = Chat::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationLabel = 'Pesan';
    protected static ?string $modelLabel = 'Pesan';
    protected static ?string $pluralModelLabel = 'Pesan';
    protected static ?string $navigationGroup = 'Komunikasi';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('sender_id')
                    ->label('Pengirim')
                    ->relationship('sender', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('receiver_id')
                    ->label('Penerima')
                    ->relationship('receiver', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('pengajuan_id')
                    ->label('Terkait Pengajuan')
                    ->relationship('pengajuan', 'id', function ($query) {
                        return $query->select('id', 'tanggalberangkat', 'kotatujuan')
                                    ->selectRaw("CONCAT('ID: ', id, ' - ', kotatujuan, ' (', tanggalberangkat, ')') as label");
                    })
                    ->getOptionLabelFromRecordUsing(fn ($record) => "ID: {$record->id} - {$record->kotatujuan} ({$record->tanggalberangkat})")
                    ->searchable()
                    ->preload()
                    ->nullable(),
                Forms\Components\Textarea::make('message')
                    ->label('Pesan')
                    ->required()
                    ->rows(4)
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_read')
                    ->label('Sudah Dibaca')
                    ->default(false)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('sender.pegawai.foto')
                    ->label('')
                    ->defaultImageUrl(fn (Chat $record) => asset('profile-photos/user.jpg'))
                    ->circular()
                    ->width(40)
                    ->height(40),
                Tables\Columns\TextColumn::make('sender.name')
                    ->label('Pengirim')
                    ->searchable()
                    ->sortable()
                    ->description(fn (Chat $record) => $record->created_at->format('d/m/Y H:i')),
                Tables\Columns\TextColumn::make('message')
                    ->label('Pesan')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('receiver.name')
                    ->label('Penerima')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pengajuan.id')
                    ->label('ID Pengajuan')
                    ->formatStateUsing(fn ($state) => $state ? "ID: {$state}" : '-')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_read')
                    ->label('Dibaca')
                    ->boolean(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('sender')
                    ->label('Pengirim')
                    ->relationship('sender', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('receiver')
                    ->label('Penerima')
                    ->relationship('receiver', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('unread')
                    ->label('Belum Dibaca')
                    ->query(fn (Builder $query) => $query->where('is_read', false)),
                Tables\Filters\Filter::make('has_pengajuan')
                    ->label('Terkait Pengajuan')
                    ->query(fn (Builder $query) => $query->whereNotNull('pengajuan_id')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('markAsRead')
                    ->label('Tandai Dibaca')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn (Chat $record) => !$record->is_read)
                    ->action(function (Chat $record) {
                        $record->update(['is_read' => true]);
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('markAsRead')
                        ->label('Tandai Dibaca')
                        ->icon('heroicon-o-check')
                        ->action(function ($records) {
                            $records->each->update(['is_read' => true]);
                        }),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChats::route('/'),
            'create' => Pages\CreateChat::route('/create'),
            'edit' => Pages\EditChat::route('/{record}/edit'),
        ];
    }
}
