<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftargradeResource\Pages;
use App\Filament\Resources\DaftargradeResource\RelationManagers;
use App\Models\Daftargrade;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Contracts\HasTable;
use stdClass;

class DaftargradeResource extends Resource
{
    protected static ?string $model = Daftargrade::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $label = 'Daftar Grade';
    protected static ?string $pluralLabel = 'Daftar Grade';
    protected static ?string $slug = 'daftar-grade';
    protected static ?string $navigationLabel = 'Daftar Grade';
    protected static ?string $recordTitleAttribute = 'namagrade';
    protected static ?string $modelLabel = 'Daftar Grade';
    protected static ?string $pluralModelLabel = 'Daftar Grade';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Card::make()
                    ->schema([
                        Forms\Components\TextInput::make('namagrade')
                            ->required()
                            ->label('Nama Grade'),
                    ])
                    ->columns(1)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('No.')->state(
                    static function (HasTable $livewire, stdClass $rowLoop): string {
                        return (string) (
                            $rowLoop->iteration +
                            ($livewire->getTableRecordsPerPage() * (
                                $livewire->getTablePage() - 1
                            ))
                        );
                    }
                ),
                Tables\Columns\TextColumn::make('namagrade')->label('Nama Grade')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDaftargrades::route('/'),
        ];
    }
}
