<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use Carbon\Carbon;

// Check pengajuan data
$pengajuans = Daftarpengajuan::take(5)->get();

echo "=== CHECKING PENGAJUAN DATA ===\n";
foreach ($pengajuans as $pengajuan) {
    echo "ID: " . $pengajuan->id . "\n";
    echo "Tanggal Berangkat: " . $pengajuan->tanggalberangkat . "\n";
    echo "Tanggal Kembali: " . $pengajuan->tanggalkembali . "\n";

    // Calculate hari manually
    $hari = 0;
    if ($pengajuan->tanggalberangkat && $pengajuan->tanggalkembali) {
        $berangkat = Carbon::parse($pengajuan->tanggalberangkat);
        $kembali = Carbon::parse($pengajuan->tanggalkembali);

        $hari = $berangkat->diffInDays($kembali) + 1;
        echo "Calculated Hari: " . $hari . "\n";
    } else {
        echo "Missing date data\n";
    }

    echo "-------------------\n";
}

// Check totaluangpengajuan data
$totaluangs = Totaluangpengajuan::take(5)->get();

echo "\n=== CHECKING TOTALUANGPENGAJUAN DATA ===\n";
foreach ($totaluangs as $totaluang) {
    echo "ID: " . $totaluang->id . "\n";
    echo "Pengajuan ID: " . $totaluang->pengajuan_id . "\n";
    echo "Hari: " . $totaluang->hari . "\n";
    echo "Nilai Tiket: " . $totaluang->nilaitiket . "\n";
    echo "Nilai Fasilitas: " . $totaluang->nilaifasilitas . "\n";
    echo "Total Nilai: " . $totaluang->totalnilai . "\n";
    echo "-------------------\n";
}

// Check if there are any pengajuan without totaluang
$pengajuansWithoutTotaluang = Daftarpengajuan::whereDoesntHave('totaluang')->get();

echo "\n=== CHECKING PENGAJUAN WITHOUT TOTALUANG ===\n";
echo "Count: " . $pengajuansWithoutTotaluang->count() . "\n";
foreach ($pengajuansWithoutTotaluang as $pengajuan) {
    echo "ID: " . $pengajuan->id . "\n";
}

echo "\nDone checking data.\n";
