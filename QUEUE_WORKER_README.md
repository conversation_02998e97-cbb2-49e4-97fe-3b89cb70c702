# Panduan Menjalankan Queue Worker

Aplikasi ini menggunakan Laravel Queue untuk memproses tugas-tugas di latar belakang, seperti ekspor data. Agar fitur ekspor berfungsi dengan baik, queue worker harus berjalan.

## Cara Menjalankan Queue Worker

### Metode 1: Menggunakan Command Line

Buka terminal atau command prompt dan jalankan perintah berikut:

```bash
php artisan queue:work database --sleep=3 --tries=3
```

<PERSON><PERSON> tutup terminal selama Anda ingin queue worker tetap berjalan.

### Metode 2: Menggunakan Script Batch (Windows)

1. Double-click pada file `start-queue-worker.bat`
2. <PERSON><PERSON> tutup jendela command prompt yang terbuka

### Metode 3: Menggunakan Command Khusus

Jalankan perintah berikut untuk memulai queue worker di latar belakang:

```bash
php artisan app:start-queue-worker
```

### Metode 4: Menggunakan Supervisor (Linux/macOS)

1. Install Supervisor:
   ```bash
   sudo apt-get install supervisor  # Ubuntu/Debian
   ```

2. Salin file `supervisor-config.conf` ke direktori konfigurasi Supervisor:
   ```bash
   sudo cp supervisor-config.conf /etc/supervisor/conf.d/laravel-worker.conf
   ```

3. Edit file konfigurasi untuk menyesuaikan path:
   ```bash
   sudo nano /etc/supervisor/conf.d/laravel-worker.conf
   ```

4. Muat ulang konfigurasi Supervisor:
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start laravel-worker:*
   ```

## Memverifikasi Queue Worker Berjalan

Untuk memverifikasi bahwa queue worker berjalan, coba lakukan ekspor data dan periksa apakah notifikasi hasil ekspor muncul setelah beberapa saat.

## Troubleshooting

Jika notifikasi ekspor tidak muncul:

1. Pastikan queue worker berjalan
2. Periksa log di `storage/logs/laravel.log` untuk error
3. Pastikan tabel `jobs` ada di database
4. Pastikan konfigurasi queue di `.env` sudah benar (QUEUE_CONNECTION=database)

## Menjalankan Queue Worker Secara Otomatis Saat Startup

### Windows

1. Buat shortcut untuk `start-queue-worker.bat`
2. Tekan `Win + R`, ketik `shell:startup`, dan tekan Enter
3. Pindahkan shortcut ke folder Startup yang terbuka

### Linux/macOS dengan Supervisor

Supervisor akan otomatis menjalankan queue worker saat sistem startup jika dikonfigurasi dengan benar.
