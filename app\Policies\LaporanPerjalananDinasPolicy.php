<?php

namespace App\Policies;

use App\Models\Daftarpengajuan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LaporanPerjalananDinasPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Semua user yang memiliki izin dapat melihat daftar laporan
        // Filter data akan ditangani di LaporanPerjalananDinasResource::getEloquentQuery()
        return $user->can('view_any_laporanperjalanandinas');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Daftarpengajuan $daftarpengajuan): bool
    {
        // Admin dapat melihat semua laporan
        if ($user->hasRole('admin')) {
            return $user->can('view_laporanperjalanandinas');
        }

        // User biasa hanya dapat melihat laporan miliknya sendiri
        if ($user->can('view_laporanperjalanandinas')) {
            // Periksa apakah user memiliki pegawai_id dan apakah laporan ini miliknya
            return $user->pegawai_id && $daftarpengajuan->PEGAWAI_ID === $user->pegawai_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Laporan tidak dapat dibuat langsung, hanya dibaca
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Daftarpengajuan $daftarpengajuan): bool
    {
        // Laporan tidak dapat diupdate, hanya dibaca
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Daftarpengajuan $daftarpengajuan): bool
    {
        // Laporan tidak dapat dihapus, hanya dibaca
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Daftarpengajuan $daftarpengajuan): bool
    {
        // Laporan tidak dapat direstore, hanya dibaca
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Daftarpengajuan $daftarpengajuan): bool
    {
        // Laporan tidak dapat dihapus permanen, hanya dibaca
        return false;
    }
}
