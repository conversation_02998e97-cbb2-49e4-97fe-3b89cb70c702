<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daftarpegawai', function (Blueprint $table) {
            $table->id();
            $table->string('nip')->unique();
            $table->string('nama')->nullable();
            $table->unsignedBigInteger('JABATAN_ID');
            $table->foreign('JABATAN_ID')->references('id')->on('daftarjabatan')->onDelete('cascade');
            $table->string('pernr')->unique()->nullable();
            $table->unsignedBigInteger('GRADE_ID');
            $table->foreign('GRADE_ID')->references('id')->on('daftargrade')->onDelete('cascade');
            $table->unsignedBigInteger('UNIT_ID');
            $table->foreign('UNIT_ID')->references('id')->on('daftarunit')->onDelete('cascade');
            $table->string('password')->nullable();
            $table->string('foto')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daftarpegawai');
    }
};
