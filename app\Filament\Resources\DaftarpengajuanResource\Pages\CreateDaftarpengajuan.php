<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateDaftarpengajuan extends CreateRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Jika user bukan admin, set PEGAWAI_ID ke pegawai_id user yang login
        $user = Auth::user();
        if ($user && !$user->hasRole('admin') && $user->pegawai_id) {
            $data['PEGAWAI_ID'] = $user->pegawai_id;
        }

        return $data;
    }
}
