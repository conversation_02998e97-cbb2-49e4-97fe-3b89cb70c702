<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daftarfasiltas', function (Blueprint $table) {
            $table->id();
            $table->string('JenjangJabatan')->nullable();
            $table->integer('Konsumsi')->nullable();
            $table->integer('Pakaian')->nullable();
            $table->integer('Transportlokal')->nullable();
            $table->unsignedBigInteger('GRADE_ID');
            $table->foreign('GRADE_ID')->references('id')->on('DaftarGrade')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daftarfasiltas');
    }
};
