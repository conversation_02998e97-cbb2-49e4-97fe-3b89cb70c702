<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\DaftarUnit;
use Filament\Widgets\ChartWidget;

class PaguPerUnitLineChart extends ChartWidget
{
    protected static ?string $heading = 'Nilai Pagu per Unit';
    protected static ?int $sort = 5; // <PERSON><PERSON><PERSON>an setelah widget grafik lainnya
    protected int | string | array $columnSpan = 'full'; // Memanjang penuh
    protected static ?string $maxHeight = '400px'; // Tinggi maksimum grafik

    protected function getData(): array
    {
        // Ambil semua unit
        $units = DaftarUnit::orderBy('nama_unit')->get();

        // Siapkan array untuk label (nama unit) dan data (nilai pagu)
        $labels = [];
        $paguValues = [];

        // Untuk setiap unit, ambil nilai pagu
        foreach ($units as $unit) {
            // Tambahkan nama unit ke labels
            $labels[] = $unit->nama_unit;

            // Ambil nilai pagu untuk unit ini
            $pagu = Daftarpagu::where('daftarunit_id', $unit->id)->value('jumlahpagu') ?? 0;
            $paguValues[] = $pagu;
        }

        // Jika tidak ada data, tambahkan data dummy untuk memastikan grafik tetap muncul
        if (empty($paguValues)) {
            $labels = ['Unit A', 'Unit B', 'Unit C'];
            $paguValues = [500000, 1000000, 1500000];
        }

        // Pastikan ada minimal 2 titik data untuk grafik garis
        if (count($paguValues) === 1) {
            $labels[] = 'Unit Lainnya';
            $paguValues[] = 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Nilai Pagu',
                    'data' => $paguValues,
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'fill' => true,
                    'tension' => 0.3, // Membuat garis lebih halus
                    'pointBackgroundColor' => 'rgba(54, 162, 235, 1)',
                    'pointBorderColor' => '#fff',
                    'pointHoverBackgroundColor' => '#fff',
                    'pointHoverBorderColor' => 'rgba(54, 162, 235, 1)',
                    'pointRadius' => 6,
                    'pointHoverRadius' => 8,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        // Hitung nilai maksimum untuk menentukan stepSize yang tepat
        $maxValue = 0;
        foreach ($this->getData()['datasets'][0]['data'] as $value) {
            if ($value > $maxValue) {
                $maxValue = $value;
            }
        }

        // Tentukan stepSize berdasarkan nilai maksimum
        $stepSize = 100000; // Default 100 ribu
        if ($maxValue > 1000000) {
            $stepSize = 500000; // 500 ribu jika nilai > 1 juta
        }
        if ($maxValue > 5000000) {
            $stepSize = 1000000; // 1 juta jika nilai > 5 juta
        }

        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        // Menampilkan nilai per 100 ribu
                        'callback' => 'function(value) {
                            if (value === 0) return "Rp 0";
                            return "Rp " + (value / 100000).toFixed(0) + " rb";
                        }',
                        'stepSize' => $stepSize,
                        'font' => [
                            'weight' => 'bold',
                            'size' => 12
                        ],
                        'padding' => 15,
                        'align' => 'end',
                        'crossAlign' => 'center',
                        'count' => 10, // Maksimal 10 label di sumbu Y
                    ],
                    'title' => [
                        'display' => true,
                        'text' => 'Nilai Pagu (dalam ratusan ribu Rupiah)',
                        'font' => [
                            'size' => 14,
                            'weight' => 'bold',
                        ],
                    ],
                    'display' => true,
                    'position' => 'left',
                    'grid' => [
                        'display' => true,
                        'color' => 'rgba(200, 200, 200, 0.3)',
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Unit',
                        'font' => [
                            'size' => 14,
                            'weight' => 'bold',
                        ],
                    ],
                    'ticks' => [
                        'maxRotation' => 45,
                        'minRotation' => 45,
                    ],
                ],
            ],
            'plugins' => [
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            var label = context.dataset.label || "";
                            if (label) {
                                label += ": ";
                            }
                            // Tampilkan nilai asli dalam tooltip
                            label += "Rp " + new Intl.NumberFormat("id-ID").format(context.raw);
                            return label;
                        }',
                    ],
                ],
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'datalabels' => [
                    'color' => '#000',
                    'font' => [
                        'weight' => 'bold',
                    ],
                    'formatter' => 'function(value) {
                        // Tampilkan nilai dalam ratusan ribu di label data
                        return "Rp " + (value / 100000).toFixed(0) + " rb";
                    }',
                    'anchor' => 'end',
                    'align' => 'top',
                    'offset' => 10,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'layout' => [
                'padding' => [
                    'top' => 20,
                    'right' => 20,
                    'bottom' => 20,
                    'left' => 60, // Tambahkan padding kiri yang lebih besar untuk ruang label
                ],
                'autoPadding' => true,
            ],
            'elements' => [
                'line' => [
                    'borderWidth' => 3,
                ],
                'point' => [
                    'radius' => 6,
                    'hoverRadius' => 8,
                ],
            ],
        ];
    }
}
