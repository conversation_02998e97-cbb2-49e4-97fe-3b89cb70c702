<?php
    $user = filament()->auth()->user();
    $items = filament()->getUserMenuItems();

    $profileItem = $items['profile'] ?? $items['account'] ?? null;
    $profileItemUrl = $profileItem?->getUrl();
    $profilePage = filament()->getProfilePage();
    $hasProfileItem = filament()->hasProfile() || filled($profileItemUrl);

    $logoutItem = $items['logout'] ?? null;

    $items = \Illuminate\Support\Arr::except($items, ['account', 'logout', 'profile']);
?>

{{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::USER_MENU_BEFORE) }}

<x-filament::dropdown
    placement="bottom-end"
    teleport
    :attributes="
        \Filament\Support\prepare_inherited_attributes($attributes)
            ->class(['fi-user-menu'])
    "
>
    <x-slot name="trigger">
        <button
            type="button"
            class="fi-user-menu-trigger flex items-center justify-center gap-x-2 rounded-lg outline-none transition duration-75 hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
        >
            <x-filament-panels::avatar.user :user="$user" />

            @if (filament()->getUserName($user))
                <span class="fi-user-menu-name text-start">
                    <span class="block truncate text-xs">
                        {{ filament()->getUserName($user) }}
                    </span>
                </span>
            @endif
        </button>
    </x-slot>

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::USER_MENU_START) }}

    @if ($hasProfileItem)
        <x-filament::dropdown.list>
            <x-filament::dropdown.list.item
                :color="$profileItem?->getColor()"
                :icon="$profileItem?->getIcon() ?? 'heroicon-m-user-circle'"
                :href="$profileItemUrl ?? filament()->getProfileUrl()"
                tag="a"
            >
                {{ $profileItem?->getLabel() ?? filament()->getProfilePageLabel() }}
            </x-filament::dropdown.list.item>
        </x-filament::dropdown.list>
    @endif

    @if (count($items))
        <x-filament::dropdown.list>
            @foreach ($items as $item)
                <x-filament::dropdown.list.item
                    :color="$item->getColor()"
                    :icon="$item->getIcon()"
                    :href="$item->getUrl()"
                    :target="$item->shouldOpenUrlInNewTab() ? '_blank' : null"
                    :tag="$item->getUrl() ? 'a' : 'button'"
                >
                    {{ $item->getLabel() }}
                </x-filament::dropdown.list.item>
            @endforeach
        </x-filament::dropdown.list>
    @endif

    <!-- Tambahkan menu Settings dan Ganti Password -->
    <x-filament::dropdown.list>
        <x-filament::dropdown.list.item
            icon="heroicon-m-cog-6-tooth"
            href="{{ route('settings.profile') }}"
            tag="a"
        >
            {{ __('Settings') }}
        </x-filament::dropdown.list.item>

        <x-filament::dropdown.list.item
            icon="heroicon-m-key"
            href="{{ route('settings.password') }}"
            tag="a"
        >
            {{ __('Ganti Password') }}
        </x-filament::dropdown.list.item>
    </x-filament::dropdown.list>

    @if ($logoutItem)
        <x-filament::dropdown.list>
            <form
                action="{{ $logoutItem->getUrl() }}"
                method="post"
                class="fi-dropdown-list-item flex w-full items-center gap-2 rounded-md p-2 text-sm transition-colors duration-75 outline-none disabled:pointer-events-none disabled:opacity-70 hover:bg-gray-50 focus-visible:bg-gray-50 dark:hover:bg-white/5 dark:focus-visible:bg-white/5"
            >
                @csrf

                <x-filament::icon
                    :icon="$logoutItem->getIcon() ?? 'heroicon-m-arrow-left-on-rectangle'"
                    class="fi-dropdown-list-item-icon h-5 w-5 text-gray-500 dark:text-gray-400"
                />

                <span>
                    {{ $logoutItem->getLabel() }}
                </span>
            </form>
        </x-filament::dropdown.list>
    @endif

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::USER_MENU_END) }}
</x-filament::dropdown>

{{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::USER_MENU_AFTER) }}
