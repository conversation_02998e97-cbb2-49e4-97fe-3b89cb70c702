<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarpengajuanResource\Pages;
use App\Models\Daftarpengajuan;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;



class DaftarpengajuanResource extends Resource
{
    protected static ?string $model = Daftarpengajuan::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Pengajuan Perjalanan Dinas';
    protected static ?string $label = 'Pengajuan Perjalanan Dinas';
    protected static ?string $pluralLabel = 'Pengajuan Perjalanan Dinas';
    protected static ?string $slug = 'daftarpengajuan';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['pegawai', 'unit', 'pegawai.grade']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Pengajuan')
                    ->schema([
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\Select::make('PEGAWAI_ID')
                                    ->relationship('pegawai', 'nama')
                                    ->placeholder('Pilih')
                                    ->required(),
                                Forms\Components\Select::make('UNIT_ID')
                                    ->relationship('unit', 'nama_unit')
                                    ->placeholder('Pilih')
                                    ->required(),
                                Forms\Components\Select::make('jenisperjalanan')
                                    ->label('Jenis Perjalanan Dinas')
                                    ->placeholder('Pilih')
                                    ->required()
                                    ->options([
                                        'PERJALANAN DINAS DIKLAT' => 'PERJALANAN DINAS DIKLAT',
                                        'PERJALANAN DINAS NON DIKLAT' => 'PERJALANAN DINAS NON DIKLAT',
                                        'PERJALANAN DINAS MUTASI JABATAN' => 'PERJALANAN DINAS MUTASI JABATAN',
                                        'PERJALANAN DINAS KESEHATAN' => 'PERJALANAN DINAS KESEHATAN',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('kotatujuan')
                                    ->label('Kota Tujuan')
                                    ->required(),
                                Fieldset::make('Tanggal Pejalanan')
                                    ->schema([
                                        Forms\Components\DatePicker::make('tanggalberangkat')
                                            ->label('Tanggal Berangkat')
                                            ->required(),
                                        Forms\Components\DatePicker::make('tanggalkembali')
                                            ->label('Tanggal Kembali')
                                            ->required(),
                                    ]),
                                Forms\Components\TextInput::make('tujuanperjalanan')
                                    ->label('Tujuan Perjalanan Dinas')
                                    ->placeholder('Tujuan Perjalanan Dinas')
                                    ->required(),
                                Forms\Components\TextInput::make('jenistransport')
                                    ->label('Jenis Transportasi')
                                    ->placeholder('Jenis Transportasi')
                                    ->required(),
                                Forms\Components\TextInput::make('tiket')
                                    ->prefix('Rp.')
                                    // ->mask(RawJs::make('$money($input)'))
                                    // ->stripCharacters('.')
                                    ->numeric(),
                                Forms\Components\TextInput::make('hotel')
                                    ->prefix('Rp.')
                                    // ->mask(RawJs::make('$money($input)'))
                                    // ->stripCharacters('.')
                                    ->numeric(),
                                Forms\Components\Textarea::make('keteranganperjalanan')
                                    ->required()
                                    ->placeholder('Keterangan Perjalanan Dinas')
                                    ->columnSpan('full'),
                                Fieldset::make('Dokumen Pendukung')
                                    ->schema([
                                        Forms\Components\FileUpload::make('formpermohonan')
                                            ->label('Form Permohonan')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('laporanperjalanan')
                                            ->label('Laporan Perjalanan Dinas')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('suratperintah')
                                            ->label('Surat Perintah Tugas')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('undangan')
                                            ->label('Undangan')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('invoicehotel')
                                            ->label('Invoice Hotel')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('invoicetiket')
                                            ->label('Invoice Tiket')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('boardingpass')
                                            ->label('Boarding Pass')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                        Forms\Components\FileUpload::make('justifikasi')
                                            ->label('Justifikasi')
                                            ->disk('public')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png']),
                                    ])
                            ]),
                    ])
                    ->columns(2)
                    ->columnSpan(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(null) // Menonaktifkan klik baris menuju halaman view
            ->defaultSort('id', 'desc')
            ->persistFiltersInSession()
            ->persistSortInSession()
            ->persistColumnSearchesInSession()
            ->persistSearchInSession()
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('pegawai.nama')
                    ->label('Nama Pegawai')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pegawai.grade.namagrade')
                    ->label('Grade')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('unit.nama_unit')
                    ->label('Unit')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('jenisperjalanan')
                    ->label('Jenis Perjalanan Dinas')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('kotatujuan')
                    ->label('Kota Tujuan')
                    ->searchable(),
                Tables\Columns\TextColumn::make('tujuanperjalanan')
                    ->label('Tujuan Perjalanan Dinas')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('tanggalberangkat')
                    ->label('Tanggal Berangkat')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('tanggalkembali')
                    ->label('Tanggal Kembali')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'PENDING' => 'warning',
                        'DISETUJUI' => 'info',
                        'SELESAI' => 'success',
                        'DITOLAK' => 'danger',
                        default => 'gray',
                    }),

                // Kolom Jumlah Hari
                Tables\Columns\TextColumn::make('totaluang.hari')
                    ->label('Jumlah Hari')
                    ->sortable()
                    ->getStateUsing(function (Daftarpengajuan $record) {
                        if ($record->totaluang) {
                            return $record->totaluang->hari;
                        }
                        return 0;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                // Kolom Nilai Tiket
                Tables\Columns\TextColumn::make('totaluang.nilaitiket')
                    ->label('Nilai Tiket')
                    ->money('IDR')
                    ->sortable()
                    ->getStateUsing(function (Daftarpengajuan $record) {
                        if ($record->totaluang) {
                            return $record->totaluang->nilaitiket;
                        }
                        return 0;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                // Kolom Nilai Fasilitas
                Tables\Columns\TextColumn::make('totaluang.nilaifasilitas')
                    ->label('Nilai Fasilitas')
                    ->money('IDR')
                    ->sortable()
                    ->getStateUsing(function (Daftarpengajuan $record) {
                        if ($record->totaluang) {
                            return $record->totaluang->nilaifasilitas;
                        }
                        return 0;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                // Kolom NILAI SPPD
                Tables\Columns\TextColumn::make('totaluang.totalnilai')
                    ->label('NILAI SPPD')
                    ->money('IDR')
                    ->sortable()
                    ->getStateUsing(function (Daftarpengajuan $record) {
                        if ($record->totaluang) {
                            return $record->totaluang->totalnilai;
                        }
                        return 0;
                    }),

                // Kolom untuk dokumen pendukung
                Tables\Columns\IconColumn::make('formpermohonan')
                    ->label('Form')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->formpermohonan))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('laporanperjalanan')
                    ->label('Laporan')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->laporanperjalanan))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('suratperintah')
                    ->label('SPT')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->suratperintah))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('undangan')
                    ->label('Undangan')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->undangan))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('invoicehotel')
                    ->label('Hotel')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->invoicehotel))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('invoicetiket')
                    ->label('Tiket')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->invoicetiket))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('boardingpass')
                    ->label('Boarding')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->boardingpass))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('justifikasi')
                    ->label('Justifikasi')
                    ->boolean()
                    ->getStateUsing(fn (Daftarpengajuan $record): bool => !empty($record->justifikasi))
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->size('md')
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Filter Status')
                    ->options([
                        'PENDING' => 'Pending',
                        'DISETUJUI' => 'Disetujui',
                        'DITOLAK' => 'Ditolak',
                        'SELESAI' => 'Selesai'
                    ])
                    ->placeholder('Semua Status')
                    ->indicator('Status'),

                Tables\Filters\SelectFilter::make('jenisperjalanan')
                    ->label('Jenis Perjalanan')
                    ->options([
                        'PERJALANAN DINAS DIKLAT' => 'Diklat',
                        'PERJALANAN DINAS NON DIKLAT' => 'Non Diklat',
                        'PERJALANAN DINAS MUTASI JABATAN' => 'Mutasi Jabatan',
                        'PERJALANAN DINAS KESEHATAN' => 'Kesehatan',
                    ])
                    ->placeholder('Semua Jenis')
                    ->indicator('Jenis Perjalanan'),

                Tables\Filters\Filter::make('tanggalberangkat')
                    ->form([
                        Forms\Components\DatePicker::make('tanggalberangkat_dari')
                            ->label('Tanggal Berangkat Dari'),
                        Forms\Components\DatePicker::make('tanggalberangkat_sampai')
                            ->label('Tanggal Berangkat Sampai'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['tanggalberangkat_dari'],
                                fn ($query, $date) => $query->whereDate('tanggalberangkat', '>=', $date),
                            )
                            ->when(
                                $data['tanggalberangkat_sampai'],
                                fn ($query, $date) => $query->whereDate('tanggalberangkat', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data) {
                        $indicators = [];

                        if ($data['tanggalberangkat_dari'] ?? null) {
                            $indicators['tanggalberangkat_dari'] = 'Berangkat dari ' . \Carbon\Carbon::parse($data['tanggalberangkat_dari'])->toFormattedDateString();
                        }

                        if ($data['tanggalberangkat_sampai'] ?? null) {
                            $indicators['tanggalberangkat_sampai'] = 'Berangkat sampai ' . \Carbon\Carbon::parse($data['tanggalberangkat_sampai'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),

                Tables\Filters\Filter::make('tanggalkembali')
                    ->form([
                        Forms\Components\DatePicker::make('tanggalkembali_dari')
                            ->label('Tanggal Kembali Dari'),
                        Forms\Components\DatePicker::make('tanggalkembali_sampai')
                            ->label('Tanggal Kembali Sampai'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['tanggalkembali_dari'],
                                fn ($query, $date) => $query->whereDate('tanggalkembali', '>=', $date),
                            )
                            ->when(
                                $data['tanggalkembali_sampai'],
                                fn ($query, $date) => $query->whereDate('tanggalkembali', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data) {
                        $indicators = [];

                        if ($data['tanggalkembali_dari'] ?? null) {
                            $indicators['tanggalkembali_dari'] = 'Kembali dari ' . \Carbon\Carbon::parse($data['tanggalkembali_dari'])->toFormattedDateString();
                        }

                        if ($data['tanggalkembali_sampai'] ?? null) {
                            $indicators['tanggalkembali_sampai'] = 'Kembali sampai ' . \Carbon\Carbon::parse($data['tanggalkembali_sampai'])->toFormattedDateString();
                        }

                        return $indicators;
                    })
            ])
            ->actions([
                // Grup aksi utama
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->label('Lihat Detail')
                        ->icon('heroicon-o-eye')
                        ->color('info'),

                    Tables\Actions\Action::make('detailUangPerjalanan')
                        ->label('Detail Uang Perjalanan')
                        ->icon('heroicon-o-currency-dollar')
                        ->color('primary')
                        ->url(fn (Daftarpengajuan $record) => static::getUrl('detail-uang', ['record' => $record->id])),

                    Tables\Actions\Action::make('uploadDokumen')
                        ->label('Upload Dokumen')
                        ->icon('heroicon-o-document-plus')
                        ->color('warning')
                        ->url(fn (Daftarpengajuan $record): string => static::getUrl('upload-dokumen', ['record' => $record])),

                    Tables\Actions\EditAction::make()
                        ->label('Edit Pengajuan')
                        ->icon('heroicon-o-pencil-square')
                        ->color('success'),
                ])
                ->label('Aksi')
                ->icon('heroicon-m-ellipsis-horizontal')
                ->size('sm')
                ->color('gray')
                ->button(),

                // Grup aksi status (hanya muncul sesuai status)
                Tables\Actions\ActionGroup::make([
                    // Action untuk menyetujui pengajuan
                    Tables\Actions\Action::make('approve')
                        ->label('Setujui Pengajuan')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->visible(fn (Daftarpengajuan $record): bool => $record->status === 'PENDING')
                        ->action(function (Daftarpengajuan $record): void {
                            $record->status = 'DISETUJUI';
                            $record->save();

                            // Tampilkan notifikasi
                            Notification::make()
                                ->title('Pengajuan disetujui')
                                ->success()
                                ->send();
                        }),

                    // Action untuk menolak pengajuan
                    Tables\Actions\Action::make('reject')
                        ->label('Tolak Pengajuan')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->visible(fn (Daftarpengajuan $record): bool => $record->status === 'PENDING')
                        ->action(function (Daftarpengajuan $record): void {
                            $record->status = 'DITOLAK';
                            $record->save();

                            // Tampilkan notifikasi
                            Notification::make()
                                ->title('Pengajuan ditolak')
                                ->danger()
                                ->send();
                        }),

                    // Action untuk menandai pengajuan sebagai selesai
                    Tables\Actions\Action::make('complete')
                        ->label('Tandai Selesai')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn (Daftarpengajuan $record): bool => $record->status === 'DISETUJUI')
                        ->action(function (Daftarpengajuan $record): void {
                            $record->status = 'SELESAI';
                            $record->save();

                            // Tampilkan notifikasi
                            Notification::make()
                                ->title('Pengajuan ditandai sebagai selesai')
                                ->success()
                                ->send();
                        }),
                ])
                ->label('Status')
                ->icon('heroicon-m-bolt')
                ->size('sm')
                ->color('warning')
                ->button()
                // Hanya tampilkan grup status jika ada aksi yang tersedia
                ->visible(fn (Daftarpengajuan $record): bool =>
                    $record->status === 'PENDING' || $record->status === 'DISETUJUI'
                ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),

            ])
        ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDaftarpengajuans::route('/'),
            'create' => Pages\CreateDaftarpengajuan::route('/create'),
            'view' => Pages\ViewPengajuan::route('/{record}'),
            'edit' => Pages\EditDaftarpengajuan::route('/{record}/edit'),
            'upload-dokumen' => Pages\UploadDokumenPengajuan::route('/{record}/upload-dokumen'),
            'detail-uang' => Pages\DetailUangPerjalanan::route('/{record}/detail-uang'),
        ];
    }
}
