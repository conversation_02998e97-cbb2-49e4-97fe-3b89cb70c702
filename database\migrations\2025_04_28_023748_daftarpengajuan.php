<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daftarpengajuan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('PEGAWAI_ID');
            $table->foreign('PEGAWAI_ID')->references('id')->on('daftarpegawai')->onDelete('cascade');
            $table->unsignedBigInteger('UNIT_ID');
            $table->foreign('UNIT_ID')->references('id')->on('daftarunit')->onDelete('cascade');
            $table->string('jenisperjalanan')->nullable();
            $table->string('kotatujuan')->nullable();
            $table->date('tanggalberangkat')->nullable();
            $table->date('tanggalkembali')->nullable();
            $table->string('tujuanperjalanan')->nullable();
            $table->string('jenistransport')->nullable();
            $table->string('keteranganperjalanan')->nullable();
            $table->string('tiket')->nullable();
            $table->string('hotel')->nullable();
            $table->string('formpermohonan')->nullable();
            $table->string('laporanperjalanan')->nullable();
            $table->string('suratperintah')->nullable();
            $table->string('undangan')->nullable();
            $table->string('invoicehotel')->nullable();
            $table->string('invoicetiket')->nullable();
            $table->string('boardingpass')->nullable();
            $table->string('justifikasi')->nullable();
            $table->string('status')->default('PENDING');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daftarpengajuan');
    }
};
