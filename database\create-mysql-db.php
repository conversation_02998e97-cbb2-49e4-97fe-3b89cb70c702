<?php

/**
 * Script untuk membuat database MySQL
 */

// Ambil konfigurasi dari .env
$host = '127.0.0.1';
$port = '3306';
$username = 'root';
$password = '';
$database = 'pln_sppd';

echo "Mencoba membuat database MySQL: {$database}\n";

try {
    // Koneksi ke MySQL tanpa database
    $pdo = new PDO("mysql:host={$host};port={$port}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Cek apakah database sudah ada
    $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$database}'");
    $exists = $stmt->fetchColumn();
    
    if ($exists) {
        echo "Database {$database} sudah ada.\n";
    } else {
        // Buat database baru
        $pdo->exec("CREATE DATABASE {$database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "Database {$database} berhasil dibuat.\n";
    }
    
    echo "Koneksi ke MySQL berhasil.\n";
} catch (PDOException $e) {
    die("Koneksi ke MySQL gagal: " . $e->getMessage() . "\n");
}
