<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use App\Models\Chat;
use App\Models\User;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewPengajuan extends ViewRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('chatAdmin')
                ->label('Ki<PERSON>esan ke Admin')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('primary')
                ->modalHeading('Kirim <PERSON>esan ke Admin')
                ->modalDescription(fn () => "Pengajuan ID: {$this->record->id} - {$this->record->kotatujuan}")
                ->modalSubmitActionLabel('Kirim')
                ->form([
                    Forms\Components\Textarea::make('message')
                        ->label('Pesan')
                        ->placeholder('Tulis pesan Anda terkait pengajuan ini...')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data): void {
                    // Dapatkan admin pertama
                    $admin = User::role('admin')->first();

                    if (!$admin) {
                        Notification::make()
                            ->title('Tidak ada admin yang tersedia')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Buat pesan chat baru
                    $chat = new Chat();
                    $chat->sender_id = Auth::id();
                    $chat->receiver_id = $admin->id;
                    $chat->pengajuan_id = $this->record->id;
                    $chat->message = $data['message'];
                    $chat->is_read = false;
                    $chat->save();

                    Notification::make()
                        ->title('Pesan berhasil dikirim ke admin')
                        ->success()
                        ->send();
                }),
            Actions\EditAction::make(),
        ];
    }
}
