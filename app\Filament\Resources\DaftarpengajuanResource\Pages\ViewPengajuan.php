<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use App\Models\Chat;
use App\Models\User;
use Filament\Actions;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewPengajuan extends ViewRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Pengajuan')
                    ->description('Detail pengajuan perjalanan dinas')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('id')
                                    ->label('ID Pengajuan')
                                    ->weight('bold'),
                                TextEntry::make('pegawai.nama')
                                    ->label('Nama Pegawai'),
                                TextEntry::make('unit.nama_unit')
                                    ->label('Unit'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('pegawai.grade.namagrade')
                                    ->label('Grade'),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'PENDING' => 'warning',
                                        'DISETUJUI' => 'success',
                                        'DITOLAK' => 'danger',
                                        'SELESAI' => 'info',
                                        default => 'gray',
                                    }),
                            ]),
                    ]),

                Section::make('Detail Perjalanan')
                    ->description('Informasi detail perjalanan dinas')
                    ->icon('heroicon-o-map-pin')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('jenisperjalanan')
                                    ->label('Jenis Perjalanan'),
                                TextEntry::make('kotatujuan')
                                    ->label('Kota Tujuan'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('tanggalberangkat')
                                    ->label('Tanggal Berangkat')
                                    ->date('d F Y'),
                                TextEntry::make('tanggalkembali')
                                    ->label('Tanggal Kembali')
                                    ->date('d F Y'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('tujuanperjalanan')
                                    ->label('Tujuan Perjalanan'),
                                TextEntry::make('jenistransport')
                                    ->label('Jenis Transportasi'),
                            ]),
                        TextEntry::make('keteranganperjalanan')
                            ->label('Keterangan Perjalanan')
                            ->columnSpanFull(),
                    ]),

                Section::make('Informasi Keuangan')
                    ->description('Detail perhitungan uang perjalanan dinas')
                    ->icon('heroicon-o-currency-dollar')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('totaluang.hari')
                                    ->label('Jumlah Hari')
                                    ->default('-'),
                                TextEntry::make('totaluang.nilaitiket')
                                    ->label('Nilai Tiket')
                                    ->money('IDR')
                                    ->default(0),
                                TextEntry::make('totaluang.nilaifasilitas')
                                    ->label('Nilai Fasilitas')
                                    ->money('IDR')
                                    ->default(0),
                            ]),
                        TextEntry::make('totaluang.totalnilai')
                            ->label('Total Nilai SPPD')
                            ->money('IDR')
                            ->weight('bold')
                            ->size('lg')
                            ->default(0),
                    ])
                    ->visible(fn ($record) => $record->totaluang !== null),

                Section::make('Status Upload Dokumen')
                    ->description('Status dokumen yang diperlukan untuk pengajuan ini')
                    ->icon('heroicon-o-document-check')
                    ->schema([
                        Grid::make(1)
                            ->schema([
                                $this->createDocumentTable(),
                            ]),
                    ]),
            ]);
    }

    protected function createDocumentTable()
    {
        $documents = [
            'formpermohonan' => 'Form Permohonan',
            'laporanperjalanan' => 'Laporan Perjalanan Dinas',
            'suratperintah' => 'Surat Perintah Tugas',
            'undangan' => 'Undangan',
            'invoicehotel' => 'Invoice Hotel',
            'invoicetiket' => 'Invoice Tiket',
            'boardingpass' => 'Boarding Pass',
            'justifikasi' => 'Justifikasi',
        ];

        return TextEntry::make('document_status')
            ->label('')
            ->formatStateUsing(function ($state, $record) use ($documents) {
                $html = '<div class="overflow-x-auto">';
                $html .= '<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">';
                $html .= '<thead class="bg-gray-50 dark:bg-gray-800">';
                $html .= '<tr>';
                $html .= '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Jenis Dokumen</th>';
                $html .= '<th class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>';
                $html .= '<th class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Aksi</th>';
                $html .= '</tr>';
                $html .= '</thead>';
                $html .= '<tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">';

                foreach ($documents as $field => $label) {
                    $fileValue = $record->{$field};
                    $isUploaded = !empty($fileValue);

                    $html .= '<tr>';
                    $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">' . $label . '</td>';

                    // Status column
                    $html .= '<td class="px-6 py-4 whitespace-nowrap text-center">';
                    if ($isUploaded) {
                        $html .= '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">';
                        $html .= '<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">';
                        $html .= '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>';
                        $html .= '</svg>';
                        $html .= 'Uploaded';
                        $html .= '</span>';
                    } else {
                        $html .= '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">';
                        $html .= '<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">';
                        $html .= '<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>';
                        $html .= '</svg>';
                        $html .= 'Not Uploaded';
                        $html .= '</span>';
                    }
                    $html .= '</td>';

                    // Action column
                    $html .= '<td class="px-6 py-4 whitespace-nowrap text-center">';
                    if ($isUploaded) {
                        $fileUrl = $this->getFileUrl($fileValue);
                        $html .= '<a href="' . $fileUrl . '" target="_blank" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">';
                        $html .= '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
                        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
                        $html .= '</svg>';
                        $html .= 'Lihat';
                        $html .= '</a>';
                    } else {
                        $html .= '<span class="text-gray-400 text-xs">-</span>';
                    }
                    $html .= '</td>';

                    $html .= '</tr>';
                }

                $html .= '</tbody>';
                $html .= '</table>';
                $html .= '</div>';

                return $html;
            })
            ->html();
    }

    protected function getFileUrl($filePath)
    {
        if (empty($filePath)) {
            return null;
        }

        // Jika file sudah menggunakan path lengkap dengan folder
        if (str_contains($filePath, 'dokumen-sppd/')) {
            return asset('storage/' . $filePath);
        }

        // Untuk file lama yang belum menggunakan folder
        return asset('storage/' . $filePath);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('chatAdmin')
                ->label('Kirim Pesan ke Admin')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('primary')
                ->modalHeading('Kirim Pesan ke Admin')
                ->modalDescription(fn () => "Pengajuan ID: {$this->record->id} - {$this->record->kotatujuan}")
                ->modalSubmitActionLabel('Kirim')
                ->form([
                    Forms\Components\Textarea::make('message')
                        ->label('Pesan')
                        ->placeholder('Tulis pesan Anda terkait pengajuan ini...')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data): void {
                    // Dapatkan admin pertama
                    $admin = User::role('admin')->first();

                    if (!$admin) {
                        Notification::make()
                            ->title('Tidak ada admin yang tersedia')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Buat pesan chat baru
                    $chat = new Chat();
                    $chat->sender_id = Auth::id();
                    $chat->receiver_id = $admin->id;
                    $chat->pengajuan_id = $this->record->id;
                    $chat->message = $data['message'];
                    $chat->is_read = false;
                    $chat->save();

                    Notification::make()
                        ->title('Pesan berhasil dikirim ke admin')
                        ->success()
                        ->send();
                }),
            Actions\EditAction::make(),
        ];
    }
}
