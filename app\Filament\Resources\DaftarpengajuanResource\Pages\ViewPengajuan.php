<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use App\Models\Chat;
use App\Models\User;
use Filament\Actions;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ViewPengajuan extends ViewRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Dokumen Pendukung')
                    ->description('Dokumen yang telah diupload untuk pengajuan ini')
                    ->icon('heroicon-o-document')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                $this->createDocumentEntry('formpermohonan', 'Form Permohonan'),
                                $this->createDocumentEntry('laporanperjalanan', 'Laporan Perjalanan Dinas'),
                                $this->createDocumentEntry('suratperintah', 'Surat Perintah'),
                                $this->createDocumentEntry('undangan', 'Undangan'),
                                $this->createDocumentEntry('invoicehotel', 'Invoice Hotel'),
                                $this->createDocumentEntry('invoicetiket', 'Invoice Tiket'),
                                $this->createDocumentEntry('boardingpass', 'Boarding Pass'),
                                $this->createDocumentEntry('justifikasi', 'Justifikasi'),
                            ]),
                    ]),
            ]);
    }

    protected function createDocumentEntry(string $field, string $label)
    {
        return TextEntry::make($field)
            ->label($label)
            ->formatStateUsing(function ($state) {
                if (empty($state)) {
                    return 'Belum diupload';
                }

                return 'Sudah diupload';
            })
            ->color(fn ($state) => empty($state) ? 'danger' : 'success')
            ->icon(fn ($state) => empty($state) ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
            ->url(function ($state) {
                if (empty($state)) {
                    return null;
                }

                return Storage::disk('public')->url($state);
            })
            ->openUrlInNewTab();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('chatAdmin')
                ->label('Kirim Pesan ke Admin')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('primary')
                ->modalHeading('Kirim Pesan ke Admin')
                ->modalDescription(fn () => "Pengajuan ID: {$this->record->id} - {$this->record->kotatujuan}")
                ->modalSubmitActionLabel('Kirim')
                ->form([
                    Forms\Components\Textarea::make('message')
                        ->label('Pesan')
                        ->placeholder('Tulis pesan Anda terkait pengajuan ini...')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data): void {
                    // Dapatkan admin pertama
                    $admin = User::role('admin')->first();

                    if (!$admin) {
                        Notification::make()
                            ->title('Tidak ada admin yang tersedia')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Buat pesan chat baru
                    $chat = new Chat();
                    $chat->sender_id = Auth::id();
                    $chat->receiver_id = $admin->id;
                    $chat->pengajuan_id = $this->record->id;
                    $chat->message = $data['message'];
                    $chat->is_read = false;
                    $chat->save();

                    Notification::make()
                        ->title('Pesan berhasil dikirim ke admin')
                        ->success()
                        ->send();
                }),
            Actions\EditAction::make(),
        ];
    }
}
