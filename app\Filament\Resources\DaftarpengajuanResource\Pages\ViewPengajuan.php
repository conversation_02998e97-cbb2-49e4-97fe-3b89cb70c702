<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use App\Models\Chat;
use App\Models\User;
use Filament\Actions;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewPengajuan extends ViewRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Pengajuan')
                    ->description('Detail pengajuan perjalanan dinas')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('id')
                                    ->label('ID Pengajuan')
                                    ->weight('bold'),
                                TextEntry::make('pegawai.nama')
                                    ->label('Nama Pegawai'),
                                TextEntry::make('unit.nama_unit')
                                    ->label('Unit'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('pegawai.grade.namagrade')
                                    ->label('Grade'),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'PENDING' => 'warning',
                                        'DISETUJUI' => 'success',
                                        'DITOLAK' => 'danger',
                                        'SELESAI' => 'info',
                                        default => 'gray',
                                    }),
                            ]),
                    ]),

                Section::make('Detail Perjalanan')
                    ->description('Informasi detail perjalanan dinas')
                    ->icon('heroicon-o-map-pin')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('jenisperjalanan')
                                    ->label('Jenis Perjalanan'),
                                TextEntry::make('kotatujuan')
                                    ->label('Kota Tujuan'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('tanggalberangkat')
                                    ->label('Tanggal Berangkat')
                                    ->date('d F Y'),
                                TextEntry::make('tanggalkembali')
                                    ->label('Tanggal Kembali')
                                    ->date('d F Y'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('tujuanperjalanan')
                                    ->label('Tujuan Perjalanan'),
                                TextEntry::make('jenistransport')
                                    ->label('Jenis Transportasi'),
                            ]),
                        TextEntry::make('keteranganperjalanan')
                            ->label('Keterangan Perjalanan')
                            ->columnSpanFull(),
                    ]),

                Section::make('Informasi Keuangan')
                    ->description('Detail perhitungan uang perjalanan dinas')
                    ->icon('heroicon-o-currency-dollar')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('totaluang.hari')
                                    ->label('Jumlah Hari')
                                    ->default('-'),
                                TextEntry::make('totaluang.nilaitiket')
                                    ->label('Nilai Tiket')
                                    ->money('IDR')
                                    ->default(0),
                                TextEntry::make('totaluang.nilaifasilitas')
                                    ->label('Nilai Fasilitas')
                                    ->money('IDR')
                                    ->default(0),
                            ]),
                        TextEntry::make('totaluang.totalnilai')
                            ->label('Total Nilai SPPD')
                            ->money('IDR')
                            ->weight('bold')
                            ->size('lg')
                            ->default(0),
                    ])
                    ->visible(fn ($record) => $record->totaluang !== null),

                Section::make('Status Upload Dokumen')
                    ->description('Status dokumen yang diperlukan untuk pengajuan ini')
                    ->icon('heroicon-o-document-check')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                $this->createDocumentEntry('formpermohonan', 'Form Permohonan'),
                                $this->createDocumentEntry('laporanperjalanan', 'Laporan Perjalanan Dinas'),
                                $this->createDocumentEntry('suratperintah', 'Surat Perintah Tugas'),
                                $this->createDocumentEntry('undangan', 'Undangan'),
                                $this->createDocumentEntry('invoicehotel', 'Invoice Hotel'),
                                $this->createDocumentEntry('invoicetiket', 'Invoice Tiket'),
                                $this->createDocumentEntry('boardingpass', 'Boarding Pass'),
                                $this->createDocumentEntry('justifikasi', 'Justifikasi'),
                            ]),
                    ]),
            ]);
    }

    protected function createDocumentEntry(string $field, string $label)
    {
        return TextEntry::make($field)
            ->label($label)
            ->formatStateUsing(function ($state) {
                if (empty($state)) {
                    return '❌ Belum diupload';
                }
                return '✅ Sudah diupload';
            })
            ->color(fn ($state) => empty($state) ? 'danger' : 'success')
            ->weight('medium')
            ->suffixAction(
                \Filament\Infolists\Components\Actions\Action::make('view')
                    ->label('Lihat')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->size('sm')
                    ->url(function ($state) {
                        if (empty($state)) {
                            return null;
                        }
                        return $this->getFileUrl($state);
                    })
                    ->openUrlInNewTab()
                    ->visible(fn ($state) => !empty($state))
            );
    }

    protected function getFileUrl($filePath)
    {
        if (empty($filePath)) {
            return null;
        }

        // Jika file sudah menggunakan path lengkap dengan folder
        if (str_contains($filePath, 'dokumen-sppd/')) {
            return asset('storage/' . $filePath);
        }

        // Untuk file lama yang belum menggunakan folder
        return asset('storage/' . $filePath);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('chatAdmin')
                ->label('Kirim Pesan ke Admin')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('primary')
                ->modalHeading('Kirim Pesan ke Admin')
                ->modalDescription(fn () => "Pengajuan ID: {$this->record->id} - {$this->record->kotatujuan}")
                ->modalSubmitActionLabel('Kirim')
                ->form([
                    Forms\Components\Textarea::make('message')
                        ->label('Pesan')
                        ->placeholder('Tulis pesan Anda terkait pengajuan ini...')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data): void {
                    // Dapatkan admin pertama
                    $admin = User::role('admin')->first();

                    if (!$admin) {
                        Notification::make()
                            ->title('Tidak ada admin yang tersedia')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Buat pesan chat baru
                    $chat = new Chat();
                    $chat->sender_id = Auth::id();
                    $chat->receiver_id = $admin->id;
                    $chat->pengajuan_id = $this->record->id;
                    $chat->message = $data['message'];
                    $chat->is_read = false;
                    $chat->save();

                    Notification::make()
                        ->title('Pesan berhasil dikirim ke admin')
                        ->success()
                        ->send();
                }),
            Actions\EditAction::make(),
        ];
    }
}
