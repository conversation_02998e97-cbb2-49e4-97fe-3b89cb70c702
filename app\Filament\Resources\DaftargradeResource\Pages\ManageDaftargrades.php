<?php

namespace App\Filament\Resources\DaftargradeResource\Pages;

use App\Filament\Resources\DaftargradeResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageDaftargrades extends ManageRecords
{
    protected static string $resource = DaftargradeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
