<?php

namespace App\Filament\Exports;

use App\Models\Daftarpengajuan;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class DaftarpengajuanExporter extends Exporter
{
    protected static ?string $model = Daftarpengajuan::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),
            ExportColumn::make('PEGAWAI_ID'),
            ExportColumn::make('UNIT_ID'),
            ExportColumn::make('jenisperjalanan'),
            ExportColumn::make('kotatujuan'),
            ExportColumn::make('tanggalberangkat'),
            ExportColumn::make('tanggalkembali'),
            ExportColumn::make('tujuanperjalanan'),
            ExportColumn::make('jenistransport'),
            ExportColumn::make('keteranganperjalanan'),
            ExportColumn::make('tiket'),
            ExportColumn::make('hotel'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your daftarpengajuan export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
