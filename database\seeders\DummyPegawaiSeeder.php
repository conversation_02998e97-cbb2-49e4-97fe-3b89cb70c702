<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Daftarpegawai;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class DummyPegawaiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Data pegawai dummy
        $pegawaiData = [
            [
                'nip' => '1234567890',
                'nama' => 'Budi Santoso',
                'jabatan_id' => 1, // Pastikan ID ini ada di tabel daftarjabatan
                'pernr' => '123456',
                'grade_id' => 1, // Pastikan ID ini ada di tabel daftargrade
                'unit_id' => 1, // Pastikan ID ini ada di tabel daftarunit
                'password' => '123456',
                'foto' => null
            ],
            [
                'nip' => '2345678901',
                'nama' => 'Siti Rahayu',
                'jabatan_id' => 2, // Pastikan ID ini ada di tabel daftarjabatan
                'pernr' => '234567',
                'grade_id' => 2, // Pastikan ID ini ada di tabel daftargrade
                'unit_id' => 2, // Pastikan ID ini ada di tabel daftarunit
                'password' => '234567',
                'foto' => null
            ],
            [
                'nip' => '3456789012',
                'nama' => 'Ahmad Wijaya',
                'jabatan_id' => 3, // Pastikan ID ini ada di tabel daftarjabatan
                'pernr' => '345678',
                'grade_id' => 3, // Pastikan ID ini ada di tabel daftargrade
                'unit_id' => 3, // Pastikan ID ini ada di tabel daftarunit
                'password' => '345678',
                'foto' => null
            ],
            [
                'nip' => '4567890123',
                'nama' => 'Dewi Lestari',
                'jabatan_id' => 1, // Pastikan ID ini ada di tabel daftarjabatan
                'pernr' => '456789',
                'grade_id' => 1, // Pastikan ID ini ada di tabel daftargrade
                'unit_id' => 1, // Pastikan ID ini ada di tabel daftarunit
                'password' => '456789',
                'foto' => null
            ],
            [
                'nip' => '5678901234',
                'nama' => 'Eko Prasetyo',
                'jabatan_id' => 2, // Pastikan ID ini ada di tabel daftarjabatan
                'pernr' => '567890',
                'grade_id' => 2, // Pastikan ID ini ada di tabel daftargrade
                'unit_id' => 2, // Pastikan ID ini ada di tabel daftarunit
                'password' => '567890',
                'foto' => null
            ],
        ];

        // Buat pegawai dan user
        foreach ($pegawaiData as $data) {
            // Buat pegawai
            $pegawai = Daftarpegawai::create([
                'nip' => $data['nip'],
                'nama' => $data['nama'],
                'JABATAN_ID' => $data['jabatan_id'],
                'pernr' => $data['pernr'],
                'GRADE_ID' => $data['grade_id'],
                'UNIT_ID' => $data['unit_id'],
                'password' => $data['password'],
                'foto' => $data['foto'],
            ]);

            // Buat user untuk pegawai
            User::create([
                'name' => $data['nama'],
                'email' => "{$data['nip']}@example.com",
                'username' => $data['nip'],
                'password' => Hash::make($data['password']),
                'pegawai_id' => $pegawai->id,
            ]);

            // Buat pengajuan SPPD untuk pegawai
            $this->createPengajuan($pegawai);
        }
    }

    /**
     * Buat pengajuan SPPD untuk pegawai
     */
    private function createPengajuan($pegawai)
    {
        // Kota tujuan
        $kotaTujuan = ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Makassar', 'Yogyakarta', 'Semarang', 'Palembang'];
        
        // Jenis perjalanan
        $jenisPerjalanan = ['Dinas', 'Training', 'Meeting', 'Workshop', 'Seminar'];
        
        // Jenis transport
        $jenisTransport = ['Pesawat', 'Kereta', 'Bus', 'Mobil Dinas'];
        
        // Status
        $status = ['PENDING', 'DISETUJUI', 'SELESAI', 'DITOLAK'];
        
        // Buat pengajuan
        $tanggalBerangkat = Carbon::now()->subDays(rand(1, 30));
        $tanggalKembali = (clone $tanggalBerangkat)->addDays(rand(1, 7));
        $hari = $tanggalBerangkat->diffInDays($tanggalKembali) + 1;
        
        $pengajuan = Daftarpengajuan::create([
            'PEGAWAI_ID' => $pegawai->id,
            'UNIT_ID' => $pegawai->UNIT_ID,
            'jenisperjalanan' => $jenisPerjalanan[array_rand($jenisPerjalanan)],
            'kotatujuan' => $kotaTujuan[array_rand($kotaTujuan)],
            'tanggalberangkat' => $tanggalBerangkat,
            'tanggalkembali' => $tanggalKembali,
            'tujuanperjalanan' => 'Keperluan ' . $jenisPerjalanan[array_rand($jenisPerjalanan)],
            'jenistransport' => $jenisTransport[array_rand($jenisTransport)],
            'keteranganperjalanan' => 'Keterangan perjalanan dinas',
            'status' => $status[array_rand($status)],
        ]);
        
        // Buat total uang pengajuan
        $nilaiTiket = rand(500000, 2000000);
        $nilaiFasilitas = rand(300000, 1000000);
        $totalNilai = ($nilaiTiket + $nilaiFasilitas) * $hari;
        
        Totaluangpengajuan::create([
            'pengajuan_id' => $pengajuan->id,
            'hari' => $hari,
            'nilaitiket' => $nilaiTiket,
            'nilaifasilitas' => $nilaiFasilitas,
            'totalnilai' => $totalNilai,
        ]);
    }
}
