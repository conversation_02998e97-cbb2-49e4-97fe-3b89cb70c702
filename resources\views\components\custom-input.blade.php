@props([
    'label' => null,
    'type' => 'text',
    'placeholder' => null,
])

<div class="w-full">
    @if ($label)
        <label class="block font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">
            {{ $label }}
        </label>
    @endif

    <input
        {{ $attributes->merge(['class' => 'w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 shadow-sm']) }}
        type="{{ $type }}"
        placeholder="{{ $placeholder }}"
    >

    @error($attributes->wire('model')->value())
        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
    @enderror
</div>
