<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

// Buat user admin
$admin = new User();
$admin->name = 'Admin';
$admin->email = '<EMAIL>';
$admin->password = Hash::make('password123');
$admin->save();

echo "User admin berhasil dibuat dengan email: <EMAIL> dan password: password123\n";
