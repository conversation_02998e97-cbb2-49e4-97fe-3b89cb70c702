<?php

namespace App\Filament\Widgets;

use App\Models\Chat;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UnreadMessagesWidget extends Widget
{
    protected static string $view = 'filament.widgets.unread-messages-widget';
    
    protected int|string|array $columnSpan = 'full';
    
    public function getUnreadMessages()
    {
        return Chat::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->with(['sender', 'pengajuan'])
            ->latest()
            ->take(5)
            ->get();
    }
    
    public function getUnreadCount()
    {
        return Chat::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->count();
    }
    
    public function markAsRead($messageId)
    {
        $message = Chat::find($messageId);
        
        if ($message && $message->receiver_id === Auth::id()) {
            $message->update(['is_read' => true]);
        }
    }
    
    public function markAllAsRead()
    {
        Chat::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }
}
