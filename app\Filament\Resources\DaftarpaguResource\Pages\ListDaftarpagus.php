<?php

namespace App\Filament\Resources\DaftarpaguResource\Pages;

use App\Filament\Resources\DaftarpaguResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDaftarpagus extends ListRecords
{
    protected static string $resource = DaftarpaguResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
