<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;

class MigrateToMysql extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:migrate-to-mysql';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate data from SQLite to MySQL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration from SQLite to MySQL...');

        // Backup .env file
        $this->info('Backing up .env file...');
        File::copy(base_path('.env'), base_path('.env.backup'));

        // Step 1: Connect to SQLite and get data
        $this->info('Connecting to SQLite database...');
        
        // Create a temporary connection to SQLite
        config(['database.connections.sqlite_old' => [
            'driver' => 'sqlite',
            'database' => database_path('database.sqlite'),
            'prefix' => '',
        ]]);

        // Get all tables from SQLite
        $tables = DB::connection('sqlite_old')
            ->select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");

        // Step 2: Run migrations on MySQL to create schema
        $this->info('Running migrations on MySQL...');
        $this->call('migrate:fresh', ['--force' => true]);

        // Step 3: Transfer data table by table
        foreach ($tables as $table) {
            $tableName = $table->name;
            
            // Skip migrations table and other Laravel system tables
            if (in_array($tableName, ['migrations', 'password_reset_tokens', 'personal_access_tokens', 'failed_jobs'])) {
                continue;
            }
            
            $this->info("Migrating data for table: {$tableName}");
            
            // Get data from SQLite
            $rows = DB::connection('sqlite_old')->table($tableName)->get();
            
            if (count($rows) === 0) {
                $this->info("No data to migrate for table: {$tableName}");
                continue;
            }
            
            // Get column names from MySQL
            $columns = Schema::getColumnListing($tableName);
            
            // Insert data into MySQL
            $chunks = $rows->chunk(100);
            $bar = $this->output->createProgressBar(count($chunks));
            
            foreach ($chunks as $chunk) {
                $data = [];
                
                foreach ($chunk as $row) {
                    $rowData = [];
                    
                    foreach ($columns as $column) {
                        // Only include columns that exist in both databases
                        if (property_exists($row, $column)) {
                            $rowData[$column] = $row->{$column};
                        }
                    }
                    
                    $data[] = $rowData;
                }
                
                if (!empty($data)) {
                    DB::table($tableName)->insert($data);
                }
                
                $bar->advance();
            }
            
            $bar->finish();
            $this->newLine();
        }
        
        // Restore original connection
        DB::purge('sqlite_old');
        
        $this->info('Migration completed successfully!');
        $this->info('Your database has been migrated from SQLite to MySQL.');
        $this->info('A backup of your .env file has been created as .env.backup');
        
        return Command::SUCCESS;
    }
}
