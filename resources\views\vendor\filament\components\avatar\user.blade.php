@props([
    'user' => filament()->auth()->user(),
])

@php
    $avatarUrl = $user->pegawai && $user->pegawai->foto ? $user->pegawai->foto : url('storage/profile-photos/user.jpg');
@endphp

<x-filament::avatar
    :src="$avatarUrl"
    :alt="__('filament-panels::layout.avatar.alt', ['name' => filament()->getUserName($user)])"
    :attributes="
        \Filament\Support\prepare_inherited_attributes($attributes)
            ->class(['fi-user-avatar'])
    "
/>
