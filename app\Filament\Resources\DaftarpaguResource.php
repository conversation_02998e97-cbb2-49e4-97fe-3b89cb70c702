<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarpaguResource\Pages;
use App\Filament\Resources\DaftarpaguResource\RelationManagers;
use App\Models\Daftarpagu;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Contracts\HasTable;
use stdClass;

class DaftarpaguResource extends Resource
{
    protected static ?string $model = Daftarpagu::class;
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $navigationLabel = 'Daftar Pagu';
    protected static ?string $label = 'Daftar Pagu';
    protected static ?string $pluralLabel = 'Daftar Pagu';
    protected static ?string $slug = 'daftar-pagu';


    protected static ?string $navigationIcon = 'heroicon-o-currency-rupee';
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Pagu')
                    ->schema([
                        Forms\Components\Select::make('daftarunit_id')
                            ->relationship('daftarUnit', 'nama_unit')
                            ->preload()
                            ->searchable()
                            ->label('Unit'),
                        Forms\Components\TextInput::make('jumlahpagu')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->label('Jumlah Pagu'),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('daftarUnit.nama_unit', 'asc')
            ->searchable()
            ->persistSearchInSession()
            ->columns([
                Tables\Columns\TextColumn::make('No.')->state(
                    static function (HasTable $livewire, stdClass $rowLoop): string {
                        return (string) (
                            $rowLoop->iteration +
                            ($livewire->getTableRecordsPerPage() * (
                                $livewire->getTablePage() - 1
                            ))
                        );
                    }
                ),
                Tables\Columns\TextColumn::make('daftarUnit.nama_unit')
                    ->label('Unit')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('jumlahpagu')
                    ->label('Jumlah Pagu')
                    ->money('IDR')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('daftarunit_id')
                    ->relationship('daftarUnit', 'nama_unit')
                    ->label('Filter berdasarkan Unit')
                    ->placeholder('Semua Unit')
                    ->preload()
                    ->searchable()
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-o-pencil-square')
                    ->color('success')
                    ->iconButton()
                    ->tooltip('Edit pagu'),
                Tables\Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->iconButton()
                    ->tooltip('Hapus pagu'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDaftarpagus::route('/'),
            'create' => Pages\CreateDaftarpagu::route('/create'),
            'edit' => Pages\EditDaftarpagu::route('/{record}/edit'),
        ];
    }
}
