<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="border-b border-gray-200 dark:border-gray-700 p-4 text-center">
            <h2 class="text-lg font-semibold">Messages</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4">
            <!-- Sidebar: Daftar Kontak -->
            <div class="col-span-1 border-r border-gray-200 dark:border-gray-700">
                <div class="p-3">
                    <input type="text" placeholder="Cari user" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm" wire:model.live.debounce.300ms="searchTerm">
                </div>

                <div class="overflow-y-auto max-h-[500px] divide-y divide-gray-200 dark:divide-gray-700">
                    <!--[if BLOCK]><![endif]--><?php if($messages->isEmpty()): ?>
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            Belum ada percakapan
                        </div>
                    <?php else: ?>
                        <?php
                            $uniqueUsers = collect();
                            foreach ($messages as $message) {
                                $otherUser = $message->sender_id == auth()->id() ? $message->receiver : $message->sender;
                                if (!$uniqueUsers->contains('id', $otherUser->id)) {
                                    $uniqueUsers->push($otherUser);
                                }
                            }
                        ?>

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $uniqueUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $latestMessage = $messages->first(function ($message) use ($user) {
                                    return ($message->sender_id == $user->id && $message->receiver_id == auth()->id()) ||
                                           ($message->sender_id == auth()->id() && $message->receiver_id == $user->id);
                                });

                                $unreadMessages = \App\Models\Chat::where('sender_id', $user->id)
                                    ->where('receiver_id', auth()->id())
                                    ->where('is_read', false)
                                    ->count();

                                $userImage = $user->pegawai && $user->pegawai->foto ? asset('storage/' . $user->pegawai->foto) : asset('storage/profile-photos/user.jpg');
                            ?>

                            <button wire:click="selectUser(<?php echo e($user->id); ?>)"
                                    class="w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
                                           <?php echo e($selectedUser == $user->id ? 'bg-gray-50 dark:bg-gray-700' : ''); ?>">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <img src="<?php echo e($userImage); ?>" alt="<?php echo e($user->name); ?>" class="w-10 h-10 rounded-full object-cover">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex justify-between items-baseline">
                                            <p class="font-medium text-sm truncate"><?php echo e($user->name); ?></p>
                                            <!--[if BLOCK]><![endif]--><?php if($unreadMessages > 0): ?>
                                                <span class="px-1.5 py-0.5 bg-primary-500 text-white rounded-full text-xs">
                                                    <?php echo e($unreadMessages); ?>

                                                </span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                            <!--[if BLOCK]><![endif]--><?php if($latestMessage->sender_id == auth()->id()): ?>
                                                <span class="text-gray-400">Anda: </span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <?php echo e(Str::limit($latestMessage->message, 30)); ?>

                                        </p>
                                    </div>
                                </div>
                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="col-span-1 md:col-span-3 flex flex-col h-[600px]">
                <!--[if BLOCK]><![endif]--><?php if($selectedUser): ?>
                    <?php
                        $chatUser = \App\Models\User::find($selectedUser);
                        $userImage = $chatUser->pegawai && $chatUser->pegawai->foto ? asset('storage/' . $chatUser->pegawai->foto) : asset('storage/profile-photos/user.jpg');
                    ?>

                    <!-- Chat Header -->
                    <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <img src="<?php echo e($userImage); ?>" alt="<?php echo e($chatUser->name); ?>" class="w-10 h-10 rounded-full object-cover">
                        </div>
                        <div>
                            <h3 class="font-medium"><?php echo e($chatUser->name); ?></h3>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="flex-1 p-4 overflow-y-auto space-y-4" id="chat-messages">
                        <?php
                            $currentDate = null;
                            $groupedMessages = $messages->groupBy(function($message) {
                                return $message->created_at->format('Y-m-d');
                            });
                        ?>

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $groupedMessages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $dateMessages): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="text-center my-2">
                                <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 px-2 py-1 rounded-full">
                                    <?php echo e(\Carbon\Carbon::parse($date)->format('d/m/Y')); ?>

                                </span>
                            </div>

                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $dateMessages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex <?php echo e($message->sender_id == auth()->id() ? 'justify-end' : 'justify-start'); ?>">
                                    <!--[if BLOCK]><![endif]--><?php if($message->sender_id != auth()->id()): ?>
                                        <div class="flex-shrink-0 mr-2">
                                            <img src="<?php echo e($userImage); ?>" alt="<?php echo e($chatUser->name); ?>" class="w-8 h-8 rounded-full object-cover">
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <div class="max-w-[70%] <?php echo e($message->sender_id == auth()->id()
                                        ? 'bg-primary-100 dark:bg-primary-700 text-primary-800 dark:text-primary-100'
                                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'); ?>

                                        rounded-lg p-3">

                                        <!--[if BLOCK]><![endif]--><?php if($message->pengajuan): ?>
                                            <div class="text-xs bg-gray-200 dark:bg-gray-600 p-1 rounded mb-2">
                                                Terkait Pengajuan: ID <?php echo e($message->pengajuan->id); ?> - <?php echo e($message->pengajuan->kotatujuan); ?>

                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <div class="whitespace-pre-wrap"><?php echo e($message->message); ?></div>

                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                                            <?php echo e($message->created_at->format('H:i')); ?>


                                            <!--[if BLOCK]><![endif]--><?php if($message->sender_id == auth()->id()): ?>
                                                <span class="ml-1">
                                                    <!--[if BLOCK]><![endif]--><?php if($message->is_read): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 text-green-500 inline']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                    <?php else: ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 text-gray-400 inline']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Chat Input -->
                    <div class="p-3 border-t border-gray-200 dark:border-gray-700">
                        <form wire:submit="sendMessage" class="flex items-end space-x-2">
                            <div class="flex-1">
                                <!--[if BLOCK]><![endif]--><?php if($this->form->getComponents()[2]): ?>
                                    <?php echo e($this->form->getComponents()[2]); ?>

                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div>
                                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['type' => 'submit','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','size' => 'sm']); ?>
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-paper-airplane'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 -rotate-45']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                            </div>
                        </form>
                    </div>
                <?php else: ?>
                    <div class="flex-1 flex items-center justify-center p-8">
                        <div class="text-center">
                            <div class="text-gray-400 dark:text-gray-500 mb-4">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chat-bubble-left-right'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-16 h-16 mx-auto']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            </div>
                            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Pilih kontak untuk memulai percakapan
                            </h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                Atau gunakan form di bawah untuk memulai percakapan baru
                            </p>

                            <div class="mt-6 max-w-md mx-auto">
                                <form wire:submit="sendMessage">
                                    <?php echo e($this->form); ?>


                                    <div class="mt-3 flex justify-end">
                                        <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['type' => 'submit']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit']); ?>
                                            Kirim
                                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', () => {
            const scrollToBottom = () => {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            };

            // Scroll to bottom on initial load
            scrollToBottom();

            // Scroll to bottom when messages are updated
            Livewire.hook('morph.updated', ({ el }) => {
                if (el.id === 'chat-messages') {
                    scrollToBottom();
                }
            });

            // Set up polling to refresh messages
            setInterval(() => {
                window.Livewire.find('<?php echo e($_instance->getId()); ?>').dispatch('refresh-messages');
            }, 10000); // Refresh every 10 seconds
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\PLN\resources\views/filament/pages/user-chat.blade.php ENDPATH**/ ?>