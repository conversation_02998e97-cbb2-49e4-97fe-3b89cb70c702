<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Daftarfasiltas;
use App\Models\Daftargrade;

// Get all grades
$grades = Daftargrade::all();

if ($grades->count() > 0) {
    echo "Creating fasilitas data for " . $grades->count() . " grades...\n";
    
    foreach ($grades as $grade) {
        // Check if fasilitas already exists for this grade
        $fasilitas = Daftarfasiltas::where('GRADE_ID', $grade->id)->first();
        
        if (!$fasilitas) {
            // Create new fasilitas with random values
            $fasilitas = new Daftarfasiltas();
            $fasilitas->JenjangJabatan = 'Jenjang untuk ' . $grade->namagrade;
            $fasilitas->Konsumsi = rand(100000, 300000); // Random value between 100k and 300k
            $fasilitas->Pakaian = rand(50000, 150000); // Random value between 50k and 150k
            $fasilitas->Transportlokal = rand(75000, 200000); // Random value between 75k and 200k
            $fasilitas->GRADE_ID = $grade->id;
            $fasilitas->save();
            
            echo "Created fasilitas for grade: " . $grade->namagrade . "\n";
        } else {
            echo "Fasilitas already exists for grade: " . $grade->namagrade . "\n";
        }
    }
    
    echo "Fasilitas data creation completed.\n";
} else {
    echo "No grades found. Please create grades first.\n";
}
