<x-filament-panels::page>
    {{ $this->infolist }}

    <div class="flex space-x-3 mt-6">
        <x-filament::button
            color="gray"
            icon="heroicon-m-arrow-left"
            icon-position="before"
            tag="a"
            href="{{ \App\Filament\Resources\DaftarpengajuanResource::getUrl('index') }}"
        >
            Ke<PERSON><PERSON> ke Daftar Pengajuan
        </x-filament::button>

        <x-filament::button
            color="primary"
            icon="heroicon-o-chat-bubble-left-right"
            x-data="{}"
            x-on:click="$dispatch('open-modal', { id: 'chat-admin-modal' })"
        >
            <PERSON><PERSON> ke Admin
        </x-filament::button>
    </div>

    <x-filament::modal id="chat-admin-modal" width="md">
        <x-slot name="heading"><PERSON><PERSON>esan ke Admin</x-slot>

        <x-slot name="description">
            Pengajuan ID: {{ $record->id }} - {{ $record->kotatujuan }}
        </x-slot>

        <form wire:submit="sendChatMessage">
            <div class="space-y-4">
                <x-filament::input.wrapper>
                    <x-filament::input.label for="chat-message">Pesan</x-filament::input.label>
                    <x-filament::input.textarea id="chat-message" wire:model="chatMessage" rows="3" placeholder="Tulis pesan Anda terkait pengajuan ini..." />
                </x-filament::input.wrapper>
            </div>

            <x-slot name="footerActions">
                <x-filament::button type="submit" wire:loading.attr="disabled">
                    Kirim
                </x-filament::button>

                <x-filament::button color="gray" x-on:click="$dispatch('close-modal', { id: 'chat-admin-modal' })">
                    Batal
                </x-filament::button>
            </x-slot>
        </form>
    </x-filament::modal>
</x-filament-panels::page>
