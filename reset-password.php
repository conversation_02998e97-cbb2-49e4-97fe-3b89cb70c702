<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

// Find user
$user = User::where('email', '<EMAIL>')->first();

if ($user) {
    // Reset password
    $user->password = Hash::make('password123');
    $user->save();

    // Create super_admin role if it doesn't exist
    $role = Role::firstOrCreate(['name' => 'super_admin']);

    // Assign role to user
    $user->assignRole($role);

    echo "Password reset successfully for user: {$user->email}\n";
} else {
    echo "User not found\n";
}
