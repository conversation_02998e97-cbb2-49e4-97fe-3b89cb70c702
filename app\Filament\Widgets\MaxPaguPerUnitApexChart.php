<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\DaftarUnit;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class MaxPaguPerUnitApexChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'maxPaguPerUnitApexChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Nilai Pagu Maksimum per Unit';

    /**
     * Sort
     */
    protected static ?int $sort = 5;

    /**
     * Widget Height
     */
    protected static ?string $maxHeight = '400px';

    /**
     * Column Span
     */
    protected int | string | array $columnSpan = 'full';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        // Ambil semua unit
        $units = DaftarUnit::orderBy('nama_unit')->get();

        // Siapkan array untuk data
        $categories = [];
        $data = [];

        // Untuk setiap unit, ambil nilai pagu maksimum
        foreach ($units as $unit) {
            $categories[] = $unit->nama_unit;
            
            // Ambil nilai pagu maksimum untuk unit ini
            $maxPagu = Daftarpagu::where('daftarunit_id', $unit->id)
                ->max('jumlahpagu') ?? 0;
            
            $data[] = (float) $maxPagu;
        }

        // Jika tidak ada data, tambahkan data dummy
        if (empty($data)) {
            $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
            $data = [25000000, 15000000, 20000000, 30000000];
        }

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 350,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Nilai Pagu Maksimum',
                    'data' => $data,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
                'title' => [
                    'text' => 'Unit',
                    'style' => [
                        'fontSize' => '14px',
                        'fontWeight' => 'bold',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontSize' => '12px',
                        'fontWeight' => '500',
                    ],
                    'rotate' => -45,
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => 'Nilai Pagu (Rupiah)',
                    'style' => [
                        'fontSize' => '14px',
                        'fontWeight' => 'bold',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontSize' => '12px',
                        'fontWeight' => '500',
                    ],
                    'formatter' => 'function(value) {
                        if (value >= 1000000000) {
                            return "Rp " + (value / 1000000000).toFixed(1) + "M";
                        } else if (value >= 1000000) {
                            return "Rp " + (value / 1000000).toFixed(1) + "Jt";
                        } else if (value >= 1000) {
                            return "Rp " + (value / 1000).toFixed(0) + "rb";
                        } else {
                            return "Rp " + value;
                        }
                    }',
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 4,
                    'horizontal' => false,
                    'columnWidth' => '60%',
                    'dataLabels' => [
                        'position' => 'top',
                    ],
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                'offsetY' => -20,
                'style' => [
                    'fontSize' => '11px',
                    'fontWeight' => 'bold',
                    'colors' => ['#304758'],
                ],
                'formatter' => 'function(value) {
                    if (value >= 1000000000) {
                        return "Rp " + (value / 1000000000).toFixed(1) + "M";
                    } else if (value >= 1000000) {
                        return "Rp " + (value / 1000000).toFixed(1) + "Jt";
                    } else if (value >= 1000) {
                        return "Rp " + (value / 1000).toFixed(0) + "rb";
                    } else {
                        return "Rp " + value;
                    }
                }',
            ],
            'colors' => ['#1f77b4'],
            'fill' => [
                'type' => 'gradient',
                'gradient' => [
                    'shade' => 'light',
                    'type' => 'vertical',
                    'shadeIntensity' => 0.25,
                    'gradientToColors' => ['#87ceeb'],
                    'inverseColors' => false,
                    'opacityFrom' => 0.85,
                    'opacityTo' => 0.55,
                    'stops' => [50, 0, 100],
                ],
            ],
            'grid' => [
                'show' => true,
                'borderColor' => '#e0e6ed',
                'strokeDashArray' => 5,
                'xaxis' => [
                    'lines' => [
                        'show' => false,
                    ],
                ],
                'yaxis' => [
                    'lines' => [
                        'show' => true,
                    ],
                ],
                'padding' => [
                    'top' => 0,
                    'right' => 0,
                    'bottom' => 0,
                    'left' => 0,
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'y' => [
                    'formatter' => 'function(value) {
                        return "Rp " + new Intl.NumberFormat("id-ID").format(value);
                    }',
                ],
            ],
        ];
    }
}
