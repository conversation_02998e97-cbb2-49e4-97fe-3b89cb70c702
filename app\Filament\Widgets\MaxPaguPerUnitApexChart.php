<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\Daftarunit;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class MaxPaguPerUnitApexChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'maxPaguPerUnitApexChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Perbandingan Nilai Pagu dan Nilai Pengajuan per Unit';

    /**
     * Sort
     */
    protected static ?int $sort = 5;

    /**
     * Widget Height
     */
    protected static ?string $maxHeight = '400px';

    /**
     * Column Span
     */
    protected int | string | array $columnSpan = 'full';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        // Gunakan data dummy untuk testing
        $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
        $paguData = [25000000, 15000000, 20000000, 30000000];
        $pengajuanData = [20000000, 12000000, 18000000, 25000000];

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 350,
            ],
            'series' => [
                [
                    'name' => 'Nilai Pagu Maksimum',
                    'data' => $paguData,
                ],
                [
                    'name' => 'Total Nilai Pengajuan',
                    'data' => $pengajuanData,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
            ],
            'colors' => ['#1f77b4', '#ff7f0e'],
        ];
    }
}
