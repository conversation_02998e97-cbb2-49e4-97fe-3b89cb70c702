<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\Daftarunit;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class MaxPaguPerUnitApexChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'maxPaguPerUnitApexChart';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Perbandingan Nilai Pagu dan Nilai Pengajuan per Unit';

    /**
     * Sort
     */
    protected static ?int $sort = 5;

    /**
     * Widget Height
     */
    protected static ?string $maxHeight = '400px';

    /**
     * Column Span
     */
    protected int | string | array $columnSpan = 'full';

    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        // Ambil data dari database
        $categories = [];
        $paguData = [];
        $pengajuanData = [];

        try {
            // Ambil semua unit dari database
            $units = Daftarunit::orderBy('nama_unit')->get();

            if ($units->count() > 0) {
                // Untuk setiap unit, ambil nilai pagu maksimum dan total nilai pengajuan
                foreach ($units as $unit) {
                    $categories[] = $unit->nama_unit;

                    // Ambil nilai pagu maksimum untuk unit ini
                    $maxPagu = Daftarpagu::where('daftarunit_id', $unit->id)
                        ->max('jumlahpagu') ?? 0;

                    // Ambil total nilai pengajuan untuk unit ini
                    $totalPengajuan = Totaluangpengajuan::whereHas('pengajuan', function($query) use ($unit) {
                        $query->where('UNIT_ID', $unit->id);
                    })->sum('totalnilai') ?? 0;

                    $paguData[] = (float) $maxPagu;
                    $pengajuanData[] = (float) $totalPengajuan;
                }
            }
        } catch (\Exception $e) {
            // Jika ada error, gunakan data dummy
            $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
            $paguData = [25000000, 15000000, 20000000, 30000000];
            $pengajuanData = [20000000, 12000000, 18000000, 25000000];
        }

        // Jika tidak ada data, gunakan data dummy
        if (empty($paguData)) {
            $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
            $paguData = [25000000, 15000000, 20000000, 30000000];
            $pengajuanData = [20000000, 12000000, 18000000, 25000000];
        }

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 350,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Nilai Pagu Maksimum',
                    'data' => $paguData,
                ],
                [
                    'name' => 'Total Nilai Pengajuan',
                    'data' => $pengajuanData,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
                'title' => [
                    'text' => 'Unit',
                    'style' => [
                        'fontSize' => '14px',
                        'fontWeight' => 'bold',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontSize' => '12px',
                        'fontWeight' => '500',
                    ],
                    'rotate' => -45,
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => 'Nilai Pagu (Rupiah)',
                    'style' => [
                        'fontSize' => '14px',
                        'fontWeight' => 'bold',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontSize' => '12px',
                        'fontWeight' => '500',
                    ],
                    'formatter' => 'function(value) {
                        if (value >= 1000000000) {
                            return "Rp " + (value / 1000000000).toLocaleString("id-ID", {maximumFractionDigits: 1}) + "M";
                        } else if (value >= 1000000) {
                            return "Rp " + (value / 1000000).toLocaleString("id-ID", {maximumFractionDigits: 1}) + "Jt";
                        } else if (value >= 1000) {
                            return "Rp " + (value / 1000).toLocaleString("id-ID", {maximumFractionDigits: 0}) + "rb";
                        } else {
                            return "Rp " + value.toLocaleString("id-ID");
                        }
                    }',
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'borderRadius' => 4,
                    'horizontal' => false,
                    'columnWidth' => '60%',
                    'dataLabels' => [
                        'position' => 'top',
                    ],
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                'offsetY' => -20,
                'style' => [
                    'fontSize' => '10px',
                    'fontWeight' => 'bold',
                    'colors' => ['#304758'],
                ],
                'formatter' => 'function(value) {
                    if (value >= 1000000000) {
                        return "Rp " + (value / 1000000000).toLocaleString("id-ID", {maximumFractionDigits: 1}) + "M";
                    } else if (value >= 1000000) {
                        return "Rp " + (value / 1000000).toLocaleString("id-ID", {maximumFractionDigits: 1}) + "Jt";
                    } else if (value >= 1000) {
                        return "Rp " + (value / 1000).toLocaleString("id-ID", {maximumFractionDigits: 0}) + "rb";
                    } else if (value > 0) {
                        return "Rp " + value.toLocaleString("id-ID");
                    } else {
                        return "";
                    }
                }',
            ],
            'colors' => ['#1f77b4', '#ff7f0e'],
            'fill' => [
                'type' => 'gradient',
                'gradient' => [
                    'shade' => 'light',
                    'type' => 'vertical',
                    'shadeIntensity' => 0.25,
                    'gradientToColors' => ['#87ceeb', '#ffcc99'],
                    'inverseColors' => false,
                    'opacityFrom' => 0.85,
                    'opacityTo' => 0.55,
                    'stops' => [50, 0, 100],
                ],
            ],
            'grid' => [
                'show' => true,
                'borderColor' => '#e0e6ed',
                'strokeDashArray' => 5,
                'xaxis' => [
                    'lines' => [
                        'show' => false,
                    ],
                ],
                'yaxis' => [
                    'lines' => [
                        'show' => true,
                    ],
                ],
                'padding' => [
                    'top' => 0,
                    'right' => 0,
                    'bottom' => 0,
                    'left' => 0,
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'shared' => true,
                'intersect' => false,
                'y' => [
                    'formatter' => 'function(value) {
                        return "Rp " + value.toLocaleString("id-ID");
                    }',
                ],
            ],
            'legend' => [
                'show' => true,
                'position' => 'top',
                'horizontalAlign' => 'center',
                'floating' => false,
                'fontSize' => '14px',
                'fontWeight' => 600,
                'offsetY' => 0,
            ],
        ];
    }
}
