<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDaftarpengajuans extends ListRecords
{
    protected static string $resource = DaftarpengajuanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
