<?php

namespace App\Console\Commands;

use App\Models\Daftarpegawai;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateUsersFromPegawai extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-users-from-pegawai';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create users from pegawai data with NIP as username and PER<PERSON> as password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating users from pegawai data...');

        // Ambil semua pegawai yang belum memiliki user
        $pegawais = Daftarpegawai::whereDoesntHave('user')->get();

        $this->info("Found {$pegawais->count()} pegawai without user accounts.");

        $count = 0;

        foreach ($pegawais as $pegawai) {
            // Skip jika NIP atau PERNR kosong
            if (empty($pegawai->nip) || empty($pegawai->pernr)) {
                $this->warn("Skipping pegawai ID {$pegawai->id}: Missing NIP or PERNR");
                continue;
            }

            // Buat user baru
            $user = new User();
            $user->name = $pegawai->nama;
            $user->username = $pegawai->nip;
            $user->email = "{$pegawai->nip}@example.com"; // Email dummy
            $user->password = Hash::make($pegawai->pernr);
            $user->pegawai_id = $pegawai->id;
            $user->save();

            $count++;

            $this->info("Created user for {$pegawai->nama} with username: {$pegawai->nip}");
        }

        $this->info("Successfully created {$count} user accounts.");

        return Command::SUCCESS;
    }
}
