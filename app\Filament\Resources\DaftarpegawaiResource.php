<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarpegawaiResource\Pages;
use App\Models\Daftarpegawai;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Contracts\HasTable;
use stdClass;

class DaftarpegawaiResource extends Resource
{
    protected static ?string $model = Daftarpegawai::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $navigationLabel = 'Daftar Pegawai';
    protected static ?string $label = 'Daftar Pegawai';
    protected static ?string $pluralLabel = 'Daftar Pegawai';
    protected static ?string $slug = 'daftar-pegawai';
    protected static ?string $recordTitleAttribute = 'nama';
    protected static ?string $modelLabel = 'Daftar Pegawai';
    protected static ?string $pluralModelLabel = 'Daftar Pegawai';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Pegawai')
                    ->schema([
                        Forms\Components\TextInput::make('nip')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('nama')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('JABATAN_ID')
                            ->label('Jabatan')
                            ->placeholder('Pilih Jabatan')
                            ->relationship('jabatan', 'nama_jabatan')
                            ->required()
                            ->searchable()
                            ->preload(),
                        Forms\Components\TextInput::make('pernr')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\Select::make('GRADE_ID')
                            ->label('Grade')
                            ->placeholder('Pilih Grade')
                            ->relationship('grade', 'namagrade')
                            ->required()
                            ->preload(),
                        Forms\Components\Select::make('UNIT_ID')
                            ->label('Unit')
                            ->placeholder('Pilih Unit')
                            ->relationship('unit', 'nama_unit')
                            ->required()
                            ->preload(),
                        Forms\Components\TextInput::make('password')
                            ->required()
                            ->maxLength(255)
                            ->password()
                            ->revealable() // Menambahkan tombol untuk melihat password
                            ->dehydrateStateUsing(fn ($state) => $state) // Tidak melakukan hashing
                            ->autocomplete('new-password'),
                        Forms\Components\FileUpload::make('foto')
                            ->label('Upload Foto')
                            ->image()
                            ->disk('public')
                            ->directory('profile-photos') // Simpan di direktori khusus
                            ->visibility('public')
                            ->maxSize(1024) // Maksimal 1MB
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('200')
                            ->imageResizeTargetHeight('200')
                            ->downloadable()
                            ->openable(),
                    ])
                    ->columns(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(null) // Menonaktifkan navigasi saat klik baris
            ->columns([
                Tables\Columns\TextColumn::make('No.')->state(
                    static function (HasTable $livewire, stdClass $rowLoop): string {
                        return (string) (
                            $rowLoop->iteration +
                            ($livewire->getTableRecordsPerPage() * (
                                $livewire->getTablePage() - 1
                            ))
                        );
                    }
                ),
                Tables\Columns\ImageColumn::make('foto')
                    ->label('Profile Picture')
                    ->circular()
                    ->size(50)
                    ->defaultImageUrl(asset('storage/profile-photos/user.jpg'))
                    ->extraImgAttributes(['loading' => 'lazy'])
                    ->extraAttributes(['class' => 'profile-picture']),
                Tables\Columns\TextColumn::make('nip')
                    ->label('NIP')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('pernr')
                    ->label('PERNR')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('grade.namagrade')
                    ->label('Grade')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('unit.nama_unit')
                    ->label('Unit')
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->color('warning'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDaftarpegawais::route('/'),
            'create' => Pages\CreateDaftarpegawai::route('/create'),
            'edit' => Pages\EditDaftarpegawai::route('/{record}/edit'),
        ];
    }
}
