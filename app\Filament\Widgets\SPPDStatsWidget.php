<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpengajuan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class SPPDStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';
    protected static ?int $sort = 2; // Tampilkan kedua
    protected static bool $isLazy = false;

    protected function getColumns(): int
    {
        // Kita akan menggunakan 4 kolom
        return 4;
    }

    protected function getStats(): array
    {
        // Hitung berdasarkan status
        $sppd_close = Daftarpengajuan::where('status', 'SELESAI')->count();
        $sppd_open = Daftarpengajuan::where('status', 'DISETUJUI')->count();
        $sppd_pending = Daftarpengajuan::where('status', 'PENDING')->count();
        $sppd_rejected = Daftarpengajuan::where('status', 'DITOLAK')->count();

        // Hitung persentase perubahan dari minggu lalu
        $lastWeek = Carbon::now()->subWeek();

        $closeLastWeek = Daftarpengajuan::where('status', 'SELESAI')->where('updated_at', '<', $lastWeek)->count();
        $openLastWeek = Daftarpengajuan::where('status', 'DISETUJUI')->where('updated_at', '<', $lastWeek)->count();
        $pendingLastWeek = Daftarpengajuan::where('status', 'PENDING')->where('created_at', '<', $lastWeek)->count();
        $rejectedLastWeek = Daftarpengajuan::where('status', 'DITOLAK')->where('updated_at', '<', $lastWeek)->count();

        // Hitung persentase perubahan
        $closeChange = $closeLastWeek > 0 ? (($sppd_close - $closeLastWeek) / $closeLastWeek) * 100 : 100;
        $openChange = $openLastWeek > 0 ? (($sppd_open - $openLastWeek) / $openLastWeek) * 100 : 100;
        $pendingChange = $pendingLastWeek > 0 ? (($sppd_pending - $pendingLastWeek) / $pendingLastWeek) * 100 : 100;
        $rejectedChange = $rejectedLastWeek > 0 ? (($sppd_rejected - $rejectedLastWeek) / $rejectedLastWeek) * 100 : 100;

        // Dapatkan data untuk chart (7 hari terakhir)
        $closeChart = $this->getChartData('SELESAI');
        $openChart = $this->getChartData('DISETUJUI');
        $pendingChart = $this->getChartData('PENDING');
        $rejectedChart = $this->getChartData('DITOLAK');

        return [
            // Widget SPPD Close (warna hijau muda)
            Stat::make('SPPD Close', $sppd_close)
                ->description(sprintf('%+.1f%% dari minggu lalu', $closeChange))
                ->descriptionIcon($closeChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('success')
                ->chart($closeChart)
                ->extraAttributes([
                    'class' => 'ring-2 ring-success-500/30 bg-success-500/10 dark:ring-success-500/20 dark:bg-success-500/5',
                    'style' => 'background-color: rgba(170, 220, 150, 0.2); border-color: rgba(170, 220, 150, 0.5);',
                ]),

            // Widget SPPD Open (warna biru)
            Stat::make('SPPD Open', $sppd_open)
                ->description(sprintf('%+.1f%% dari minggu lalu', $openChange))
                ->descriptionIcon($openChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('info')
                ->chart($openChart)
                ->extraAttributes([
                    'class' => 'ring-2 ring-info-500/30 bg-info-500/10 dark:ring-info-500/20 dark:bg-info-500/5',
                    'style' => 'background-color: rgba(0, 180, 240, 0.2); border-color: rgba(0, 180, 240, 0.5);',
                ]),

            // Widget SPPD Pending (warna kuning)
            Stat::make('SPPD Pending', $sppd_pending)
                ->description(sprintf('%+.1f%% dari minggu lalu', $pendingChange))
                ->descriptionIcon($pendingChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('warning')
                ->chart($pendingChart)
                ->extraAttributes([
                    'class' => 'ring-2 ring-warning-500/30 bg-warning-500/10 dark:ring-warning-500/20 dark:bg-warning-500/5',
                    'style' => 'background-color: rgba(255, 200, 0, 0.2); border-color: rgba(255, 200, 0, 0.5);',
                ]),

            // Widget SPPD Ditolak (warna merah)
            Stat::make('SPPD Ditolak', $sppd_rejected)
                ->description(sprintf('%+.1f%% dari minggu lalu', $rejectedChange))
                ->descriptionIcon($rejectedChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('danger')
                ->chart($rejectedChart)
                ->extraAttributes([
                    'class' => 'ring-2 ring-danger-500/30 bg-danger-500/10 dark:ring-danger-500/20 dark:bg-danger-500/5',
                    'style' => 'background-color: rgba(255, 50, 50, 0.2); border-color: rgba(255, 50, 50, 0.5);',
                ]),
        ];
    }

    protected function getChartData(string $status): array
    {
        $days = 7;
        $data = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');

            if ($status === 'all') {
                $count = Daftarpengajuan::whereDate('created_at', $date)->count();
            } else {
                $count = Daftarpengajuan::where('status', $status)
                    ->whereDate('updated_at', $date)
                    ->count();
            }

            $data[] = $count;
        }

        return $data;
    }
}
