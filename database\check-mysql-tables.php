<?php

/**
 * Script untuk memeriksa tabel-tabel di database MySQL
 */

// Ambil konfigurasi dari .env
$host = '127.0.0.1';
$port = '3306';
$username = 'root';
$password = '';
$database = 'pln_sppd';

echo "Memeriksa tabel-tabel di database MySQL: {$database}\n";

try {
    // Koneksi ke MySQL
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Dapatkan daftar tabel
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "Tidak ada tabel di database {$database}.\n";
    } else {
        echo "Daftar tabel di database {$database}:\n";
        foreach ($tables as $table) {
            echo "- {$table}\n";
        }
    }
    
    echo "\nKoneksi ke MySQL berhasil.\n";
} catch (PDOException $e) {
    die("Koneksi ke MySQL gagal: " . $e->getMessage() . "\n");
}
