<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Daftarpengajuan extends Model
{
    use HasFactory;

    protected $guarded= [];
    protected $table = 'daftarpengajuan';

    public function jabatan()
    {
        return $this->belongsTo(daftarjabatan::class, 'JABATAN_ID');
    }

    public function grade()
    {
        return $this->belongsTo(daftargrade::class, 'GRADE_ID');
    }

    public function unit()
    {
        return $this->belongsTo(daftarunit::class, 'UNIT_ID');
    }

    public function pegawai()
    {
        return $this->belongsTo(daftarpegawai::class, 'PEGAWAI_ID');
    }

    /**
     * Get the total uang for this pengajuan.
     */
    public function totaluang()
    {
        return $this->hasOne(Totaluangpengajuan::class, 'pengajuan_id');
    }

    /**
     * Alias for totaluang to match the exporter naming
     */
    public function totaluangpengajuan()
    {
        return $this->hasOne(Totaluangpengajuan::class, 'pengajuan_id');
    }

    /**
     * Calculate the number of days between tanggalberangkat and tanggalkembali
     */
    public function getJumlahHariAttribute()
    {
        if ($this->tanggalberangkat && $this->tanggalkembali) {
            $berangkat = \Carbon\Carbon::parse($this->tanggalberangkat);
            $kembali = \Carbon\Carbon::parse($this->tanggalkembali);

            // Hitung selisih hari antara tanggal berangkat dan kembali
            // diffInDays mengembalikan selisih absolut, jadi tidak perlu khawatir tentang urutan
            // +1 karena hari keberangkatan dan kepulangan dihitung
            return $berangkat->diffInDays($kembali) + 1;
        }

        return 0;
    }
}
