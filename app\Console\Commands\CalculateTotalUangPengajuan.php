<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use App\Models\Daftarfasiltas;
use Carbon\Carbon;

class CalculateTotalUangPengajuan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-total-uang-pengajuan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate and populate total uang pengajuan data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting calculation of total uang pengajuan...');

        // Get all pengajuan
        $pengajuans = Daftarpengajuan::all();
        $count = 0;

        foreach ($pengajuans as $pengajuan) {
            // Calculate hari
            $hari = 0;
            if ($pengajuan->tanggalberangkat && $pengajuan->tanggalkembali) {
                $berangkat = Carbon::parse($pengajuan->tanggalberangkat);
                $kembali = Carbon::parse($pengajuan->tanggalkembali);

                // Hitung selisih hari antara tanggal berangkat dan kembali
                // diffInDays mengembalikan selisih absolut, jadi tidak perlu khawatir tentang urutan
                // +1 karena hari keberangkatan dan kepulangan dihitung
                $hari = $berangkat->diffInDays($kembali) + 1;
            }

            // Calculate nilaitiket (tiket + hotel)
            $nilaitiket = (float)$pengajuan->tiket + (float)$pengajuan->hotel;

            // Get fasilitas based on grade
            $fasilitas = null;
            if ($pengajuan->pegawai && $pengajuan->pegawai->GRADE_ID) {
                $fasilitas = Daftarfasiltas::where('GRADE_ID', $pengajuan->pegawai->GRADE_ID)->first();
            }

            // Calculate nilaifasilitas
            $nilaifasilitas = 0;
            if ($fasilitas && $hari > 0) {
                $konsumsi = (float)$fasilitas->Konsumsi * $hari;
                $pakaian = (float)$fasilitas->Pakaian * max(0, $hari - 1);
                $transportlokal = (float)$fasilitas->Transportlokal * $hari;

                $nilaifasilitas = $konsumsi + $pakaian + $transportlokal;
            }

            // Calculate total nilai
            $totalnilai = $nilaitiket + $nilaifasilitas;

            // Create or update totaluangpengajuan record
            Totaluangpengajuan::updateOrCreate(
                ['pengajuan_id' => $pengajuan->id],
                [
                    'hari' => $hari,
                    'nilaitiket' => $nilaitiket,
                    'nilaifasilitas' => $nilaifasilitas,
                    'totalnilai' => $totalnilai,
                ]
            );

            $count++;
        }

        $this->info("Calculation completed for {$count} pengajuan records.");
    }
}
