<?php
    $unreadMessages = $this->getUnreadMessages();
    $unreadCount = $this->getUnreadCount();
?>

<!--[if BLOCK]><![endif]--><?php if($unreadCount > 0): ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium">
                Pesan Belum Dibaca (<?php echo e($unreadCount); ?>)
            </h2>
            
            <button wire:click="markAllAsRead" class="text-sm text-primary-600 hover:text-primary-500">
                Tandai semua dibaca
            </button>
        </div>
        
        <div class="space-y-3">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $unreadMessages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div class="font-medium"><?php echo e($message->sender->name); ?></div>
                        <div class="flex space-x-2">
                            <span class="text-xs text-gray-500"><?php echo e($message->created_at->format('d M Y H:i')); ?></span>
                            <button wire:click="markAsRead(<?php echo e($message->id); ?>)" class="text-gray-400 hover:text-gray-600">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            </button>
                        </div>
                    </div>
                    
                    <!--[if BLOCK]><![endif]--><?php if($message->pengajuan): ?>
                        <div class="text-xs bg-gray-200 dark:bg-gray-700 p-1 rounded my-1">
                            Terkait Pengajuan: ID <?php echo e($message->pengajuan->id); ?> - <?php echo e($message->pengajuan->kotatujuan); ?>

                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    
                    <div class="text-sm mt-1 text-gray-700 dark:text-gray-300">
                        <?php echo e(Str::limit($message->message, 100)); ?>

                    </div>
                    
                    <div class="mt-2">
                        <a href="<?php echo e(route('filament.admin.pages.user-chat')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                            Balas
                        </a>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        
        <!--[if BLOCK]><![endif]--><?php if($unreadCount > 5): ?>
            <div class="mt-3 text-center">
                <a href="<?php echo e(route('filament.admin.pages.user-chat')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                    Lihat semua pesan (<?php echo e($unreadCount - 5); ?> lainnya)
                </a>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php /**PATH C:\Users\<USER>\PLN\resources\views/filament/widgets/unread-messages-widget.blade.php ENDPATH**/ ?>