<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('totaluangpengajuan', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pengajuan_id');
            $table->integer('hari')->default(0);
            $table->decimal('nilaitiket', 15, 2)->default(0);
            $table->decimal('nilaifasilitas', 15, 2)->default(0);
            $table->decimal('totalnilai', 15, 2)->default(0);
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('pengajuan_id')->references('id')->on('daftarpengajuan')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('totaluangpengajuan');
    }
};
