<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Totaluangpengajuan extends Model
{
    use HasFactory;

    protected $table = 'totaluangpengajuan';
    
    protected $fillable = [
        'pengajuan_id',
        'hari',
        'nilaitiket',
        'nilaifasilitas',
        'totalnilai',
    ];
    
    /**
     * Get the pengajuan that owns the total uang.
     */
    public function pengajuan()
    {
        return $this->belongsTo(Daftarpengajuan::class, 'pengajuan_id');
    }
    
    /**
     * Calculate the total nilai (nilaitiket + nilaifasilitas)
     */
    public function calculateTotalNilai()
    {
        $this->totalnilai = $this->nilaitiket + $this->nilaifasilitas;
        return $this->totalnilai;
    }
}
