<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Daftarpegawai;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\DB;

class DaftarpegawaiPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_daftarpegawai');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Admin dapat melihat semua data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('view_daftarpegawai');
        }

        // User biasa hanya dapat melihat data pegawai miliknya sendiri
        if ($user->can('view_daftarpegawai')) {
            return $user->pegawai_id && $daftarpegawai->id === $user->pegawai_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_daftarpegawai');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Admin dapat mengupdate semua data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('update_daftarpegawai');
        }

        // User biasa hanya dapat mengupdate data pegawai miliknya sendiri
        if ($user->can('update_daftarpegawai')) {
            return $user->pegawai_id && $daftarpegawai->id === $user->pegawai_id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Hanya admin yang dapat menghapus data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('delete_daftarpegawai');
        }

        // User biasa tidak dapat menghapus data pegawai, bahkan miliknya sendiri
        return false;
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_daftarpegawai');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Hanya admin yang dapat menghapus permanen data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('force_delete_daftarpegawai');
        }

        // User biasa tidak dapat menghapus permanen data pegawai
        return false;
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_daftarpegawai');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Hanya admin yang dapat memulihkan data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('restore_daftarpegawai');
        }

        // User biasa tidak dapat memulihkan data pegawai
        return false;
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_daftarpegawai');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Daftarpegawai $daftarpegawai): bool
    {
        // Admin dapat mereplikasi semua data pegawai
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('replicate_daftarpegawai');
        }

        // User biasa hanya dapat mereplikasi data pegawai miliknya sendiri
        if ($user->can('replicate_daftarpegawai')) {
            return $user->pegawai_id && $daftarpegawai->id === $user->pegawai_id;
        }

        return false;
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_daftarpegawai');
    }
}
