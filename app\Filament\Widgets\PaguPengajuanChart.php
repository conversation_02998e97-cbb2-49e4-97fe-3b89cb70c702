<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Daftarpagu;
use App\Models\Daftarunit;
use App\Models\Totaluangpengajuan;

class PaguPengajuanChart extends ChartWidget
{
    protected static ?string $heading = 'Perbandingan <PERSON> dan <PERSON> per Unit';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        // Gunakan data dummy untuk testing
        $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
        $paguData = [25000000, 15000000, 20000000, 30000000];
        $pengajuanData = [20000000, 12000000, 18000000, 25000000];

        return [
            'datasets' => [
                [
                    'label' => 'Ni<PERSON> Maksimum',
                    'data' => $paguData,
                    'backgroundColor' => '#1f77b4',
                ],
                [
                    'label' => 'Total Nilai Pengajuan',
                    'data' => $pengajuanData,
                    'backgroundColor' => '#ff7f0e',
                ],
            ],
            'labels' => $categories,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 5000000, // 5 juta
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
        ];
    }
}
