<?php

namespace App\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use App\Models\Daftarpagu;
use App\Models\Daftarunit;
use App\Models\Totaluangpengajuan;

class PaguPengajuanChart extends ChartWidget
{
    protected static ?string $heading = 'Perbandingan Nilai Pagu dan Nilai Pengajuan per Unit';

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        // Ambil data real dari database
        $categories = [];
        $paguData = [];
        $pengajuanData = [];

        try {
            // Ambil semua unit dari database
            $units = Daftarunit::orderBy('nama_unit')->get();

            if ($units->count() > 0) {
                // Untuk setiap unit, ambil nilai pagu maksimum dan total nilai pengajuan
                foreach ($units as $unit) {
                    $categories[] = $unit->nama_unit;

                    // Ambil nilai pagu maksimum untuk unit ini
                    $maxPagu = Daftarpagu::where('daftarunit_id', $unit->id)
                        ->max('jumlahpagu') ?? 0;

                    // Ambil total nilai pengajuan untuk unit ini
                    $totalPengajuan = Totaluangpengajuan::whereHas('pengajuan', function($query) use ($unit) {
                        $query->where('UNIT_ID', $unit->id);
                    })->sum('totalnilai') ?? 0;

                    $paguData[] = (float) $maxPagu;
                    $pengajuanData[] = (float) $totalPengajuan;
                }
            }
        } catch (\Exception $e) {
            // Jika ada error, gunakan data dummy
            $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
            $paguData = [25000000, 15000000, 20000000, 30000000];
            $pengajuanData = [20000000, 12000000, 18000000, 25000000];
        }

        // Jika tidak ada data, gunakan data dummy
        if (empty($paguData)) {
            $categories = ['FINANCE', 'HR', 'IT', 'OPERATIONS'];
            $paguData = [25000000, 15000000, 20000000, 30000000];
            $pengajuanData = [20000000, 12000000, 18000000, 25000000];
        }

        return [
            'datasets' => [
                [
                    'label' => 'Nilai Pagu Maksimum',
                    'data' => $paguData,
                    'backgroundColor' => '#1f77b4',
                ],
                [
                    'label' => 'Total Nilai Pengajuan',
                    'data' => $pengajuanData,
                    'backgroundColor' => '#ff7f0e',
                ],
            ],
            'labels' => $categories,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 5000000, // 5 juta
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
        ];
    }
}
