<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'Manajemen Pengguna';
    protected static ?string $navigationLabel = 'Pengguna';
    protected static ?string $label = 'Pengguna';
    protected static ?string $pluralLabel = 'Pengguna';
    protected static ?string $slug = 'pengguna';
    protected static ?string $recordTitleAttribute = 'name';
    protected static ?string $modelLabel = 'Pengguna';
    protected static ?string $pluralModelLabel = 'Pengguna';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\Fieldset::make('User Details')
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->label('Full Name'),
                    Forms\Components\TextInput::make('email')
                        ->email()
                        ->required()
                        ->label('Email Address'),
                    Forms\Components\TextInput::make('password')
                        ->password()
                        ->required()
                        ->label('Password')
                        ->revealable(),
                    Forms\Components\Select::make('roles')
                        ->relationship('roles', 'name')
                        ->multiple()
                        ->preload()
                        ->searchable(),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                    Tables\Columns\TextColumn::make('id')->label('ID')->sortable(),
                    Tables\Columns\TextColumn::make('name')->label('Name')->searchable(),
                    Tables\Columns\TextColumn::make('email')->label('Email')->searchable(),
                    Tables\Columns\TextColumn::make('roles.name')->label('Role')->searchable(),
                    Tables\Columns\TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
                ])
                ->filters([
                    Tables\Filters\Filter::make('active')->query(fn(Builder $query) => $query->whereNotNull('email_verified_at')),
                ])
                ->actions([
                    Tables\Actions\EditAction::make(),
                ])
                ->bulkActions([
                    Tables\Actions\BulkActionGroup::make([
                        Tables\Actions\DeleteBulkAction::make(),
                    ]),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
