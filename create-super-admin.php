<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

// Create user
$user = new User();
$user->name = 'Super Admin';
$user->email = '<EMAIL>';
$user->password = Hash::make('password123');
$user->save();

// Create super_admin role if it doesn't exist
$role = Role::firstOrCreate(['name' => 'super_admin']);

// Assign role to user
$user->assignRole($role);

echo "Super Admin user created successfully\n";
