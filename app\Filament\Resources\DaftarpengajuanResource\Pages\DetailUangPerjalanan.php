<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use App\Models\Daftarfasiltas;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use Carbon\Carbon;
use Filament\Resources\Pages\Page;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\IconPosition;

class DetailUangPerjalanan extends Page
{
    protected static string $resource = DaftarpengajuanResource::class;

    protected static string $view = 'filament.resources.daftarpengajuan-resource.pages.detail-uang-perjalanan';
    
    public ?Daftarpengajuan $record = null;
    
    public function mount(Daftarpengajuan $record): void
    {
        $this->record = $record;
        
        // Pastikan data totaluang sudah ada
        if (!$record->totaluang) {
            // Hitung hari
            $hari = 0;
            if ($record->tanggalberangkat && $record->tanggalkembali) {
                $berangkat = Carbon::parse($record->tanggalberangkat);
                $kembali = Carbon::parse($record->tanggalkembali);
                $hari = $berangkat->diffInDays($kembali) + 1;
            }
            
            // Hitung nilai tiket
            $nilaitiket = (float)$record->tiket + (float)$record->hotel;
            
            // Hitung nilai fasilitas
            $nilaifasilitas = 0;
            $fasilitas = null;
            
            if ($record->pegawai && $record->pegawai->GRADE_ID) {
                $fasilitas = Daftarfasiltas::where('GRADE_ID', $record->pegawai->GRADE_ID)->first();
                
                if ($fasilitas && $hari > 0) {
                    $konsumsi = (float)$fasilitas->Konsumsi * $hari;
                    $pakaian = (float)$fasilitas->Pakaian * max(0, $hari - 1);
                    $transportlokal = (float)$fasilitas->Transportlokal * $hari;
                    
                    $nilaifasilitas = $konsumsi + $pakaian + $transportlokal;
                }
            }
            
            // Hitung total nilai
            $totalnilai = $nilaitiket + $nilaifasilitas;
            
            // Buat record totaluang
            Totaluangpengajuan::create([
                'pengajuan_id' => $record->id,
                'hari' => $hari,
                'nilaitiket' => $nilaitiket,
                'nilaifasilitas' => $nilaifasilitas,
                'totalnilai' => $totalnilai,
            ]);
            
            // Refresh record
            $this->record->refresh();
        }
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        $record = $this->record;
        $totaluang = $record->totaluang;
        
        // Hitung nilai-nilai yang diperlukan
        $hari = $totaluang ? $totaluang->hari : 0;
        
        // Dapatkan data fasilitas
        $fasilitas = null;
        if ($record->pegawai && $record->pegawai->GRADE_ID) {
            $fasilitas = Daftarfasiltas::where('GRADE_ID', $record->pegawai->GRADE_ID)->first();
        }
        
        // Hitung komponen fasilitas
        $konsumsiPerHari = $fasilitas ? (float)$fasilitas->Konsumsi : 0;
        $pakaianPerHari = $fasilitas ? (float)$fasilitas->Pakaian : 0;
        $transportlokalPerHari = $fasilitas ? (float)$fasilitas->Transportlokal : 0;
        
        $totalKonsumsi = $konsumsiPerHari * $hari;
        $totalPakaian = $pakaianPerHari * max(0, $hari - 1);
        $totalTransportLokal = $transportlokalPerHari * $hari;
        
        return $infolist
            ->record($record)
            ->schema([
                Section::make('Informasi Perjalanan Dinas')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('pegawai.nama')
                                    ->label('Nama Pegawai'),
                                TextEntry::make('pegawai.grade.namagrade')
                                    ->label('Grade'),
                                TextEntry::make('unit.nama_unit')
                                    ->label('Unit'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('tanggalberangkat')
                                    ->label('Tanggal Berangkat')
                                    ->date('d F Y'),
                                TextEntry::make('tanggalkembali')
                                    ->label('Tanggal Kembali')
                                    ->date('d F Y'),
                            ]),
                        TextEntry::make('totaluang.hari')
                            ->label('Jumlah Hari')
                            ->state($hari)
                            ->weight(FontWeight::Bold),
                    ]),
                
                Section::make('Detail Uang Perjalanan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('konsumsi_per_hari')
                                    ->label('Konsumsi per Hari')
                                    ->state($konsumsiPerHari)
                                    ->money('IDR'),
                                TextEntry::make('total_konsumsi')
                                    ->label('Total Konsumsi')
                                    ->state($totalKonsumsi)
                                    ->money('IDR')
                                    ->weight(FontWeight::Bold),
                                
                                TextEntry::make('pakaian_per_hari')
                                    ->label('Pakaian per Hari')
                                    ->state($pakaianPerHari)
                                    ->money('IDR'),
                                TextEntry::make('total_pakaian')
                                    ->label('Total Pakaian')
                                    ->state($totalPakaian)
                                    ->money('IDR')
                                    ->weight(FontWeight::Bold)
                                    ->helperText('Dihitung untuk ' . max(0, $hari - 1) . ' hari'),
                                
                                TextEntry::make('transportlokal_per_hari')
                                    ->label('Transport Lokal per Hari')
                                    ->state($transportlokalPerHari)
                                    ->money('IDR'),
                                TextEntry::make('total_transportlokal')
                                    ->label('Total Transport Lokal')
                                    ->state($totalTransportLokal)
                                    ->money('IDR')
                                    ->weight(FontWeight::Bold),
                                
                                TextEntry::make('tiket')
                                    ->label('Nilai Tiket')
                                    ->money('IDR'),
                                TextEntry::make('hotel')
                                    ->label('Nilai Hotel')
                                    ->money('IDR'),
                            ]),
                    ]),
                
                Section::make('Total Nilai SPPD')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('totaluang.nilaitiket')
                                    ->label('Total Nilai Tiket')
                                    ->state(function () use ($record) {
                                        return $record->totaluang ? $record->totaluang->nilaitiket : 0;
                                    })
                                    ->money('IDR')
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('totaluang.nilaifasilitas')
                                    ->label('Total Nilai Fasilitas')
                                    ->state(function () use ($record) {
                                        return $record->totaluang ? $record->totaluang->nilaifasilitas : 0;
                                    })
                                    ->money('IDR')
                                    ->weight(FontWeight::Bold),
                            ]),
                        TextEntry::make('totaluang.totalnilai')
                            ->label('TOTAL NILAI SPPD')
                            ->state(function () use ($record) {
                                return $record->totaluang ? $record->totaluang->totalnilai : 0;
                            })
                            ->money('IDR')
                            ->size('text-xl')
                            ->weight(FontWeight::Bold)
                            ->color('primary'),
                    ]),
            ]);
    }
}
