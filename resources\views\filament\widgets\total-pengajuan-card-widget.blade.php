<div class="filament-widget w-full">
    <div class="p-2 space-y-2 bg-white rounded-xl shadow filament-stats-overview-widget w-full">
        <div class="grid gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 w-full">
            <div class="p-6 rounded-lg filament-stats-card bg-gray-50 relative w-full" style="background-color: rgba(220, 220, 220, 0.3); border-color: rgba(180, 180, 180, 0.5); width: 100%;">
                <div class="flex items-center justify-between gap-4">
                    <div>
                        <h2 class="text-sm font-medium tracking-tight text-gray-500 filament-stats-card-heading">
                            Total Pengajuan SPPD
                        </h2>

                        <div class="text-3xl font-semibold tracking-tight text-gray-950 filament-stats-card-value">
                            {{ $totalPengajuan }}
                        </div>

                        <div class="flex items-center gap-1 text-sm font-medium text-gray-500 filament-stats-card-description">
                            <span class="{{ $totalChange >= 0 ? 'text-success-500' : 'text-danger-500' }}">
                                {{ sprintf('%+.1f%%', $totalChange) }} dari minggu lalu
                            </span>

                            @if ($totalChange >= 0)
                                <svg class="w-4 h-4 text-success-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-4 h-4 text-danger-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12 13a1 1 0 110 2H7a1 1 0 01-1-1V9a1 1 0 112 0v2.586l4.293-4.293a1 1 0 011.414 0L16 9.586V7a1 1 0 112 0v5a1 1 0 01-1 1h-5z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                    </div>

                    <div class="w-32 h-12">
                        <div class="filament-stats-card-chart">
                            <div class="sparkline" style="width: 100%; height: 100%;">
                                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                                    @php
                                        $chartData = $totalChart;
                                        $max = max($chartData) ?: 1;
                                        $points = [];

                                        // Calculate points for the sparkline
                                        $width = 100;
                                        $height = 40;
                                        $step = count($chartData) > 1 ? $width / (count($chartData) - 1) : 0;

                                        foreach ($chartData as $i => $value) {
                                            $x = $i * $step;
                                            $y = $height - ($value / $max * $height);
                                            $points[] = "$x,$y";
                                        }

                                        $pointsString = implode(' ', $points);
                                    @endphp

                                    <polyline
                                        fill="none"
                                        stroke="#9ca3af"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        points="{{ $pointsString }}"
                                    ></polyline>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
