<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Daftarpengajuan;
use App\Models\Daftarpegawai;
use App\Models\Daftarunit;
use App\Models\Daftarjabatan;
use App\Models\Daftargrade;

// Pastikan ada data pegawai dan unit
$pegawai = Daftarpegawai::first();
$unit = Daftarunit::first();

if (!$pegawai) {
    echo "Tidak ada data pegawai. Membuat data pegawai dummy...\n";

    // Pastikan data jabatan, grade, dan unit sudah ada
    $jabatan = Daftarjabatan::first();
    $grade = Daftargrade::first();
    $unit = Daftarunit::first();

    if (!$jabatan || !$grade || !$unit) {
        echo "Data jabatan, grade, atau unit belum tersedia. Jalankan create-base-data.php terlebih dahulu.\n";
        exit;
    }

    $pegawai = new Daftarpegawai();
    $pegawai->nip = '123456789';
    $pegawai->nama = 'Pegawai Dummy';
    $pegawai->JABATAN_ID = $jabatan->id;
    $pegawai->pernr = '123456';
    $pegawai->GRADE_ID = $grade->id;
    $pegawai->UNIT_ID = $unit->id;
    $pegawai->password = bcrypt('password');
    $pegawai->save();

    echo "Data pegawai berhasil dibuat.\n";
}

if (!$unit) {
    echo "Tidak ada data unit. Membuat data unit dummy...\n";
    $unit = new Daftarunit();
    $unit->nama_unit = 'Unit Dummy';
    $unit->save();
}

// Buat data pengajuan dengan status berbeda
$statuses = ['PENDING', 'DISETUJUI', 'SELESAI', 'DITOLAK'];

foreach ($statuses as $status) {
    // Buat beberapa pengajuan untuk setiap status
    $count = rand(3, 8);
    for ($i = 0; $i < $count; $i++) {
        $pengajuan = new Daftarpengajuan();
        $pengajuan->PEGAWAI_ID = $pegawai->id;
        $pengajuan->UNIT_ID = $unit->id;
        $pengajuan->jenisperjalanan = 'PERJALANAN DINAS ' . ($i % 2 == 0 ? 'DIKLAT' : 'NON DIKLAT');
        $pengajuan->kotatujuan = 'Kota ' . $i;
        $pengajuan->tanggalberangkat = now()->addDays($i);
        $pengajuan->tanggalkembali = now()->addDays($i + rand(1, 5));
        $pengajuan->tujuanperjalanan = 'Tujuan perjalanan ' . $i;
        $pengajuan->jenistransport = 'Transportasi ' . $i;
        $pengajuan->keteranganperjalanan = 'Keterangan perjalanan ' . $i;
        $pengajuan->status = $status;
        $pengajuan->save();
    }

    echo "Berhasil membuat $count data pengajuan dengan status $status\n";
}

echo "Selesai membuat data dummy\n";
