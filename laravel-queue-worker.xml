<?xml version="1.0" encoding="UTF-8"?>
<service>
  <id>laravel-queue-worker</id>
  <name><PERSON><PERSON> Queue Worker</name>
  <description><PERSON><PERSON> Queue Worker Service for PLN Application</description>
  <executable>php</executable>
  <arguments>artisan queue:work database --sleep=3 --tries=3</arguments>
  <logpath>%BASE%\storage\logs</logpath>
  <log mode="roll-by-size">
    <sizeThreshold>10240</sizeThreshold>
    <keepFiles>8</keepFiles>
  </log>
  <workingdirectory>%BASE%</workingdirectory>
  <onfailure action="restart" />
</service>
