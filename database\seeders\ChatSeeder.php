<?php

namespace Database\Seeders;

use App\Models\Chat;
use App\Models\Daftarpengajuan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class ChatSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Dapatkan beberapa user untuk percakapan
        $adminUser = User::role('admin')->first();

        if (!$adminUser) {
            $this->command->info('Tidak ada user admin. Membuat user admin baru...');
            $adminUser = User::create([
                'name' => 'Admin PLN',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
            $adminUser->assignRole('admin');
        }

        $regularUsers = User::whereDoesntHave('roles', function ($query) {
            $query->where('name', 'admin');
        })->take(3)->get();

        if ($regularUsers->isEmpty()) {
            $this->command->info('Tidak ada user reguler. Membuat user reguler baru...');
            for ($i = 1; $i <= 3; $i++) {
                $user = User::create([
                    'name' => "Pegawai PLN $i",
                    'email' => "pegawai$<EMAIL>",
                    'password' => bcrypt('password'),
                ]);
                $regularUsers->push($user);
            }
        }

        // Dapatkan beberapa pengajuan untuk referensi dalam chat
        $pengajuanList = Daftarpengajuan::take(5)->get();

        if ($pengajuanList->isEmpty()) {
            $this->command->info('Tidak ada data pengajuan. Chat akan dibuat tanpa referensi pengajuan.');
        }

        // Hapus chat yang sudah ada
        Chat::truncate();

        $this->command->info('Membuat percakapan dummy...');

        // Buat percakapan untuk setiap user reguler
        foreach ($regularUsers as $index => $user) {
            $pengajuan = $pengajuanList->isNotEmpty() ? $pengajuanList->random() : null;
            $pengajuanId = $pengajuan ? $pengajuan->id : null;

            // Percakapan 1: User bertanya tentang pengajuan
            $this->createConversation1($user, $adminUser, $pengajuanId);

            // Percakapan 2: User memberikan feedback
            $this->createConversation2($user, $adminUser);

            // Percakapan 3: User menanyakan informasi umum
            $this->createConversation3($user, $adminUser);
        }

        $this->command->info('Berhasil membuat ' . Chat::count() . ' pesan dummy.');
    }

    /**
     * Buat percakapan tentang pengajuan
     */
    private function createConversation1($user, $admin, $pengajuanId = null)
    {
        $now = Carbon::now();

        // Pesan 1: User bertanya tentang pengajuan
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'pengajuan_id' => $pengajuanId,
            'message' => "Selamat pagi, saya ingin menanyakan status pengajuan SPPD saya. Apakah sudah diproses?",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(2),
            'updated_at' => $now->copy()->subDays(2),
        ]);

        // Pesan 2: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'pengajuan_id' => $pengajuanId,
            'message' => "Selamat pagi, terima kasih atas pertanyaannya. Saya sudah mengecek pengajuan Anda dan saat ini sedang dalam proses review. Estimasi 1-2 hari kerja akan selesai.",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(2)->addHours(1),
            'updated_at' => $now->copy()->subDays(2)->addHours(1),
        ]);

        // Pesan 3: User bertanya lagi
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'pengajuan_id' => $pengajuanId,
            'message' => "Terima kasih atas informasinya. Apakah ada dokumen tambahan yang perlu saya lampirkan untuk mempercepat proses?",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(2)->addHours(2),
            'updated_at' => $now->copy()->subDays(2)->addHours(2),
        ]);

        // Pesan 4: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'pengajuan_id' => $pengajuanId,
            'message' => "Untuk saat ini dokumen yang Anda lampirkan sudah lengkap. Jika ada kebutuhan dokumen tambahan, kami akan menghubungi Anda kembali.",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(1),
            'updated_at' => $now->copy()->subDays(1),
        ]);

        // Pesan 5: User berterima kasih
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'pengajuan_id' => $pengajuanId,
            'message' => "Baik, terima kasih banyak atas bantuannya!",
            'is_read' => false,
            'created_at' => $now->copy()->subHours(12),
            'updated_at' => $now->copy()->subHours(12),
        ]);
    }

    /**
     * Buat percakapan feedback
     */
    private function createConversation2($user, $admin)
    {
        $now = Carbon::now();

        // Pesan 1: User memberikan feedback
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'message' => "Halo, saya ingin memberikan feedback tentang sistem SPPD yang baru. Menurut saya sistem ini sangat membantu dan mempermudah proses pengajuan. Terima kasih!",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(5),
            'updated_at' => $now->copy()->subDays(5),
        ]);

        // Pesan 2: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'message' => "Terima kasih banyak atas feedback positifnya! Kami senang mendengar bahwa sistem baru ini membantu mempermudah proses pengajuan. Jika ada saran atau masukan lain, jangan ragu untuk memberi tahu kami.",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(5)->addHours(3),
            'updated_at' => $now->copy()->subDays(5)->addHours(3),
        ]);

        // Pesan 3: User memberikan saran
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'message' => "Sama-sama. Saya punya saran kecil, mungkin bisa ditambahkan fitur notifikasi via email ketika status pengajuan berubah, sehingga kami tidak perlu selalu mengecek aplikasi.",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(4),
            'updated_at' => $now->copy()->subDays(4),
        ]);

        // Pesan 4: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'message' => "Terima kasih atas sarannya! Itu ide yang sangat bagus. Kami sedang mengembangkan fitur notifikasi email dan akan diimplementasikan dalam update sistem berikutnya. Kami sangat menghargai masukan Anda untuk perbaikan sistem.",
            'is_read' => false,
            'created_at' => $now->copy()->subDays(3),
            'updated_at' => $now->copy()->subDays(3),
        ]);
    }

    /**
     * Buat percakapan informasi umum
     */
    private function createConversation3($user, $admin)
    {
        $now = Carbon::now();

        // Pesan 1: User bertanya informasi
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'message' => "Selamat siang, saya ingin menanyakan informasi tentang batas maksimal nilai hotel yang bisa diajukan untuk perjalanan dinas ke Jakarta. Terima kasih.",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(1)->subHours(5),
            'updated_at' => $now->copy()->subDays(1)->subHours(5),
        ]);

        // Pesan 2: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'message' => "Selamat siang, untuk perjalanan dinas ke Jakarta, batas maksimal nilai hotel yang dapat diajukan adalah Rp 1.000.000 per malam untuk pegawai grade 1-10, dan Rp 1.500.000 per malam untuk pegawai grade 11 ke atas. Apakah ada informasi lain yang Anda butuhkan?",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(1)->subHours(4),
            'updated_at' => $now->copy()->subDays(1)->subHours(4),
        ]);

        // Pesan 3: User bertanya lagi
        Chat::create([
            'sender_id' => $user->id,
            'receiver_id' => $admin->id,
            'message' => "Terima kasih informasinya. Saya juga ingin tahu apakah ada daftar hotel rekanan PLN yang direkomendasikan untuk perjalanan dinas ke Jakarta?",
            'is_read' => true,
            'created_at' => $now->copy()->subDays(1)->subHours(3),
            'updated_at' => $now->copy()->subDays(1)->subHours(3),
        ]);

        // Pesan 4: Admin merespon
        Chat::create([
            'sender_id' => $admin->id,
            'receiver_id' => $user->id,
            'message' => "Berikut adalah daftar hotel rekanan PLN di Jakarta:\n\n1. Hotel Mulia Senayan\n2. Hotel Indonesia Kempinski\n3. Aryaduta Hotel Jakarta\n4. Aston Priority Simatupang\n5. Mercure Jakarta Sabang\n\nUntuk pemesanan, Anda bisa menghubungi bagian Travel Management di ext. 7890 atau <NAME_EMAIL>",
            'is_read' => false,
            'created_at' => $now->copy()->subHours(2),
            'updated_at' => $now->copy()->subHours(2),
        ]);
    }
}
