<?php

namespace App\Policies;

use App\Models\Chat;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\DB;

class ChatPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Semua user yang memiliki izin dapat melihat daftar chat
        // Filter data akan ditangani di ChatResource::getEloquentQuery()
        return $user->can('view_any_chat');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Chat $chat): bool
    {
        // Admin dapat melihat semua pesan
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('view_chat');
        }

        // User biasa hanya dapat melihat pesan yang dikirim atau diterima olehnya
        if ($user->can('view_chat')) {
            return $chat->sender_id === $user->id || $chat->receiver_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_chat');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Chat $chat): bool
    {
        // Admin dapat mengupdate semua pesan
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('update_chat');
        }

        // User biasa hanya dapat mengupdate pesan yang dikirim olehnya
        if ($user->can('update_chat')) {
            return $chat->sender_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Chat $chat): bool
    {
        // Admin dapat menghapus semua pesan
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('delete_chat');
        }

        // User biasa hanya dapat menghapus pesan yang dikirim olehnya
        if ($user->can('delete_chat')) {
            return $chat->sender_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        // Hanya admin yang dapat melakukan bulk delete
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        return $isAdmin && $user->can('delete_any_chat');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Chat $chat): bool
    {
        // Admin dapat merestore semua pesan
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('restore_chat');
        }

        // User biasa hanya dapat merestore pesan yang dikirim olehnya
        if ($user->can('restore_chat')) {
            return $chat->sender_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Chat $chat): bool
    {
        // Admin dapat menghapus permanen semua pesan
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        if ($isAdmin) {
            return $user->can('force_delete_chat');
        }

        // User biasa hanya dapat menghapus permanen pesan yang dikirim olehnya
        if ($user->can('force_delete_chat')) {
            return $chat->sender_id === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can bulk permanently delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        // Hanya admin yang dapat melakukan bulk force delete
        $isAdmin = DB::table('model_has_roles')
            ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_id', $user->id)
            ->where('roles.name', 'admin')
            ->exists();

        return $isAdmin && $user->can('force_delete_any_chat');
    }
}
