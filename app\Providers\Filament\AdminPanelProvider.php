<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;

use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{


    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login(\App\Filament\Pages\Auth\Login::class)
            ->colors([
                'primary' => Color::Amber,
            ])
            ->navigationGroups([
                'Manajemen Pengguna',
                'Manajemen Data',
                'Komunikasi',
                'Laporan',
            ])
            ->databaseNotifications()
            ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
            ->userMenuItems([
                MenuItem::make()
                    ->label('Pesan')
                    ->url(fn (): string => route('filament.admin.pages.user-chat'))
                    ->icon('heroicon-o-chat-bubble-left-right'),
                MenuItem::make()
                    ->label('Settings')
                    ->url(fn (): string => route('settings.profile'))
                    ->icon('heroicon-o-cog'),
                MenuItem::make()
                    ->label('Ganti Password')
                    ->icon('heroicon-o-key')
                    ->url(fn (): string => route('settings.password')),
            ])
            ->navigationItems([
                NavigationItem::make('Pesan')
                    ->url(fn (): string => route('filament.admin.pages.user-chat'))
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->group('Komunikasi')
                    ->sort(1),
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->resources([
                \App\Filament\Resources\RoleResource::class,
                \App\Filament\Resources\UserResource::class,
            ])
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            // Nonaktifkan auto-discovery untuk widget agar tidak menemukan widget yang dinonaktifkan
            // ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class, // Widget Welcome dihapus sesuai permintaan
                // \App\Filament\Widgets\UnreadMessagesWidget::class, // Widget pesan belum dibaca dihapus sesuai permintaan
                \App\Filament\Widgets\FullWidthTotalPengajuanWidget::class, // Widget Total Pengajuan SPPD yang memanjang penuh
                \App\Filament\Widgets\SPPDStatsWidget::class,
                \App\Filament\Widgets\PaguVsPengajuanBarChart::class, // Grafik batang perbandingan nilai pagu dan pengajuan per unit
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make()
            ]);
    }
}