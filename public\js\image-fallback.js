/**
 * Script untuk menangani error loading gambar
 * Akan mengganti gambar yang gagal dimuat dengan gambar default
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fungsi untuk menangani error loading gambar
    function handleImageError(img) {
        img.onerror = null; // Mencegah infinite loop
        img.src = '/storage/profile-photos/user.jpg'; // Ganti dengan gambar default
    }

    // Tambahkan event listener ke semua gambar
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('error', function() {
            handleImageError(this);
        });
    });

    // Tambahkan observer untuk menangani gambar yang ditambahkan secara dinamis
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const images = node.querySelectorAll('img');
                        images.forEach(img => {
                            img.addEventListener('error', function() {
                                handleImageError(this);
                            });
                        });
                        
                        // Jika node itu sendiri adalah img
                        if (node.tagName === 'IMG') {
                            node.addEventListener('error', function() {
                                handleImageError(this);
                            });
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
