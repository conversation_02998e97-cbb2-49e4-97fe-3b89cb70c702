<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpengajuan;
use Carbon\Carbon;
use Filament\Widgets\Widget;

class FullWidthTotalPengajuanWidget extends Widget
{
    protected static ?int $sort = 1;

    // Menggunakan 'full' agar memanjang penuh
    protected int | string | array $columnSpan = 'full';

    protected function getViewData(): array
    {
        // Hitung total pengajuan
        $totalPengajuan = Daftarpengajuan::count();

        // Hitung persentase perubahan dari minggu lalu
        $lastWeek = Carbon::now()->subWeek();
        $totalLastWeek = Daftarpengajuan::where('created_at', '<', $lastWeek)->count();
        $totalChange = $totalLastWeek > 0 ? (($totalPengajuan - $totalLastWeek) / $totalLastWeek) * 100 : 100;

        // Dapatkan data untuk chart (7 hari terakhir)
        $totalChart = $this->getChartData();

        return [
            'totalPengajuan' => $totalPengajuan,
            'totalChange' => $totalChange,
            'totalChart' => $totalChart,
        ];
    }

    protected function getChartData(): array
    {
        $data = [];
        
        // Dapatkan data untuk 7 hari terakhir
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $startOfDay = Carbon::parse($date)->startOfDay();
            $endOfDay = Carbon::parse($date)->endOfDay();
            
            $count = Daftarpengajuan::whereBetween('created_at', [$startOfDay, $endOfDay])->count();
            
            $data[] = $count;
        }
        
        return $data;
    }

    protected static string $view = 'filament.widgets.full-width-total-pengajuan-widget';
}
