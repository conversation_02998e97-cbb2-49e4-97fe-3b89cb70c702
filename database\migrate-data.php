<?php

/**
 * <PERSON>ript untuk migrasi data dari SQLite ke MySQL
 */

echo "Memulai migrasi data dari SQLite ke MySQL...\n";

// Koneksi ke SQLite
try {
    $sqlite = new PDO("sqlite:" . __DIR__ . "/database.sqlite");
    $sqlite->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Koneksi ke SQLite berhasil.\n";
} catch (PDOException $e) {
    die("Koneksi ke SQLite gagal: " . $e->getMessage() . "\n");
}

// Koneksi ke MySQL
try {
    // Ambil konfigurasi dari .env
    $envContent = file_get_contents(__DIR__ . '/../.env');
    preg_match('/DB_HOST=([^\n]+)/', $envContent, $matches);
    $host = $matches[1] ?? '127.0.0.1';
    
    preg_match('/DB_PORT=([^\n]+)/', $envContent, $matches);
    $port = $matches[1] ?? '3306';
    
    preg_match('/DB_DATABASE=([^\n]+)/', $envContent, $matches);
    $database = $matches[1] ?? 'pln_sppd';
    
    preg_match('/DB_USERNAME=([^\n]+)/', $envContent, $matches);
    $username = $matches[1] ?? 'root';
    
    preg_match('/DB_PASSWORD=([^\n]+)/', $envContent, $matches);
    $password = $matches[1] ?? '';
    
    $mysql = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $mysql->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Koneksi ke MySQL berhasil.\n";
} catch (PDOException $e) {
    die("Koneksi ke MySQL gagal: " . $e->getMessage() . "\n");
}

// Dapatkan daftar tabel dari SQLite
try {
    $stmt = $sqlite->query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        die("Tidak ada tabel di database SQLite.\n");
    }
    
    echo "Daftar tabel di database SQLite:\n";
    foreach ($tables as $table) {
        echo "- {$table}\n";
    }
} catch (PDOException $e) {
    die("Gagal mendapatkan daftar tabel dari SQLite: " . $e->getMessage() . "\n");
}

// Migrasi data tabel per tabel
foreach ($tables as $table) {
    // Skip tabel sistem Laravel
    if (in_array($table, ['migrations', 'password_reset_tokens', 'personal_access_tokens', 'failed_jobs'])) {
        echo "Melewati tabel sistem: {$table}\n";
        continue;
    }
    
    echo "\nMemulai migrasi data untuk tabel: {$table}\n";
    
    try {
        // Dapatkan data dari SQLite
        $stmt = $sqlite->query("SELECT * FROM {$table}");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($rows)) {
            echo "Tidak ada data di tabel {$table}.\n";
            continue;
        }
        
        echo "Jumlah baris yang akan dimigrasi: " . count($rows) . "\n";
        
        // Hapus data yang ada di tabel MySQL
        $mysql->exec("DELETE FROM {$table}");
        
        // Dapatkan kolom dari baris pertama
        $columns = array_keys($rows[0]);
        $columnList = implode(', ', $columns);
        
        // Siapkan statement untuk insert
        $placeholders = implode(', ', array_fill(0, count($columns), '?'));
        $stmt = $mysql->prepare("INSERT INTO {$table} ({$columnList}) VALUES ({$placeholders})");
        
        // Insert data
        $mysql->beginTransaction();
        
        foreach ($rows as $row) {
            $stmt->execute(array_values($row));
        }
        
        $mysql->commit();
        
        echo "Migrasi data untuk tabel {$table} berhasil.\n";
    } catch (PDOException $e) {
        if ($mysql->inTransaction()) {
            $mysql->rollBack();
        }
        echo "Gagal migrasi data untuk tabel {$table}: " . $e->getMessage() . "\n";
    }
}

echo "\nMigrasi data selesai!\n";
echo "Database telah berhasil dimigrasi dari SQLite ke MySQL.\n";
