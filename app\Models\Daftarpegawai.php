<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Models\User;

class Daftarpegawai extends Model
{
    use HasFactory;

    protected $guarded= [];
    protected $table = 'daftarpegawai';

    // Mencegah password di-hash secara otomatis
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = $value;
    }

    // Accessor untuk foto - menggunakan format Laravel 8+
    protected function foto(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                // Jika tidak ada foto, gunakan foto default
                if (empty($value)) {
                    return url('storage/profile-photos/user.jpg');
                }

                // Jika foto sudah berupa URL lengkap, kembalikan apa adanya
                if (filter_var($value, FILTER_VALIDATE_URL)) {
                    return $value;
                }

                // Jika tidak, buat URL lengkap
                return url('storage/' . $value);
            },
        );
    }
    // protected $fillable = [
    //     'nip',
    //     'nama',
    //     'JABATAN_ID',
    //     'pernr',
    //     'GRADE_ID',
    //     'UNIT_ID',
    //     'password',
    //     'foto'
    // ];

    public function jabatan()
    {
        return $this->belongsTo(daftarjabatan::class, 'JABATAN_ID');
    }

    public function grade()
    {
        return $this->belongsTo(daftargrade::class, 'GRADE_ID');
    }

    public function unit()
    {
        return $this->belongsTo(daftarunit::class, 'UNIT_ID');
    }

    /**
     * Get the user associated with the pegawai.
     */
    public function user()
    {
        return $this->hasOne(User::class, 'pegawai_id');
    }
}
