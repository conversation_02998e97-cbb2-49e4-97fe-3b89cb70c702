@php
    $unreadMessages = $this->getUnreadMessages();
    $unreadCount = $this->getUnreadCount();
@endphp

@if($unreadCount > 0)
    <x-filament::section>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium">
                Pesan Belum Dibaca ({{ $unreadCount }})
            </h2>
            
            <button wire:click="markAllAsRead" class="text-sm text-primary-600 hover:text-primary-500">
                Tandai semua dibaca
            </button>
        </div>
        
        <div class="space-y-3">
            @foreach($unreadMessages as $message)
                <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div class="font-medium">{{ $message->sender->name }}</div>
                        <div class="flex space-x-2">
                            <span class="text-xs text-gray-500">{{ $message->created_at->format('d M Y H:i') }}</span>
                            <button wire:click="markAsRead({{ $message->id }})" class="text-gray-400 hover:text-gray-600">
                                <x-heroicon-o-check class="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                    
                    @if($message->pengajuan)
                        <div class="text-xs bg-gray-200 dark:bg-gray-700 p-1 rounded my-1">
                            Terkait Pengajuan: ID {{ $message->pengajuan->id }} - {{ $message->pengajuan->kotatujuan }}
                        </div>
                    @endif
                    
                    <div class="text-sm mt-1 text-gray-700 dark:text-gray-300">
                        {{ Str::limit($message->message, 100) }}
                    </div>
                    
                    <div class="mt-2">
                        <a href="{{ route('filament.admin.pages.user-chat') }}" class="text-sm text-primary-600 hover:text-primary-500">
                            Balas
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
        
        @if($unreadCount > 5)
            <div class="mt-3 text-center">
                <a href="{{ route('filament.admin.pages.user-chat') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    Lihat semua pesan ({{ $unreadCount - 5 }} lainnya)
                </a>
            </div>
        @endif
    </x-filament::section>
@endif
