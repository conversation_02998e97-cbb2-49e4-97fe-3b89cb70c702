<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\Daftarpengajuan;
use App\Models\Totaluangpengajuan;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class PaguVsTotalSPPDChart extends ChartWidget
{
    protected static ?string $heading = 'Perbandingan Nilai Pagu dan Total SPPD';
    protected static ?int $sort = 3; // Tampilkan setelah widget status
    protected int | string | array $columnSpan = 6; // Setengah layar (dari 12 kolom)

    protected function getData(): array
    {
        // Mendapatkan total pagu dari semua unit
        $totalPagu = Daftarpagu::sum('jumlahpagu');

        // Mendapatkan total nilai SPPD dari semua pengajuan
        $totalSPPD = Totaluangpengajuan::sum('totalnilai');

        // Mendapatkan total nilai SPPD berdasarkan status
        $totalSPPDByStatus = Daftarpengajuan::select('status', DB::raw('SUM(totaluangpengajuan.totalnilai) as total'))
            ->leftJoin('totaluangpengajuan', 'daftarpengajuan.id', '=', 'totaluangpengajuan.pengajuan_id')
            ->groupBy('status')
            ->get()
            ->pluck('total', 'status')
            ->toArray();

        // Nilai default jika tidak ada data
        $totalSPPDSelesai = $totalSPPDByStatus['SELESAI'] ?? 0;
        $totalSPPDDisetujui = $totalSPPDByStatus['DISETUJUI'] ?? 0;
        $totalSPPDPending = $totalSPPDByStatus['PENDING'] ?? 0;
        $totalSPPDDitolak = $totalSPPDByStatus['DITOLAK'] ?? 0;

        return [
            'datasets' => [
                [
                    'label' => 'Nilai (Rupiah)',
                    'data' => [
                        $totalPagu, 
                        $totalSPPD, 
                        $totalSPPDSelesai, 
                        $totalSPPDDisetujui, 
                        $totalSPPDPending, 
                        $totalSPPDDitolak
                    ],
                    'backgroundColor' => [
                        'rgba(54, 162, 235, 0.5)', // Biru untuk Total Pagu
                        'rgba(255, 99, 132, 0.5)', // Merah untuk Total SPPD
                        'rgba(75, 192, 192, 0.5)', // Hijau untuk SPPD Selesai
                        'rgba(54, 162, 235, 0.5)', // Biru untuk SPPD Disetujui
                        'rgba(255, 206, 86, 0.5)', // Kuning untuk SPPD Pending
                        'rgba(255, 99, 132, 0.5)'  // Merah untuk SPPD Ditolak
                    ],
                    'borderColor' => [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    'borderWidth' => 1
                ],
            ],
            'labels' => ['Total Pagu', 'Total SPPD', 'SPPD Selesai', 'SPPD Disetujui', 'SPPD Pending', 'SPPD Ditolak'],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + new Intl.NumberFormat("id-ID").format(value); }',
                    ],
                ],
            ],
            'plugins' => [
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { 
                            var label = context.label || "";
                            if (label) {
                                label += ": ";
                            }
                            label += "Rp " + new Intl.NumberFormat("id-ID").format(context.raw);
                            return label;
                        }',
                    ],
                ],
                'legend' => [
                    'display' => false,
                ],
                'datalabels' => [
                    'color' => '#000',
                    'font' => [
                        'weight' => 'bold',
                    ],
                    'formatter' => 'function(value) { 
                        if (value > 1000000) {
                            return "Rp " + (value / 1000000).toFixed(1) + " Juta"; 
                        }
                        return "Rp " + new Intl.NumberFormat("id-ID").format(value);
                    }',
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
