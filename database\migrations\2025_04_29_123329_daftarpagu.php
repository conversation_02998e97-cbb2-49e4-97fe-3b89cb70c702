<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daftarpagu', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('daftarunit_id');
            $table->foreign('daftarunit_id')->references('id')->on('daftarunit')->onDelete('cascade');
            $table->bigInteger('jumlahpagu');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daftarpagu');
    }
};
