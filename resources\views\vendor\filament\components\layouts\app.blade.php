@php
    $livewireId = $this->getId();
@endphp

<x-filament::layouts.base :livewire-id="$livewireId">
    <div class="filament-app-layout flex h-full w-full overflow-x-clip">
        <div
            x-data="{}"
            x-cloak
            x-show="$store.sidebar.isOpen"
            x-transition.opacity.500ms
            x-on:click="$store.sidebar.close()"
            class="filament-sidebar-close-overlay fixed inset-0 z-20 h-full w-full bg-gray-900/50 lg:hidden"
        ></div>

        <x-filament::layouts.app.sidebar />

        <div
            @if (config('filament.layout.sidebar.is_collapsible_on_desktop'))
                x-data="{}"
                x-bind:class="{
                    'lg:pl-[var(--collapsed-sidebar-width)] rtl:lg:pr-[var(--collapsed-sidebar-width)] rtl:lg:pl-0 lg:ml-0 rtl:lg:mr-0': ! $store.sidebar.isOpen,
                    'lg:pl-[var(--sidebar-width)] rtl:lg:pr-[var(--sidebar-width)] rtl:lg:pl-0': $store.sidebar.isOpen,
                }"
                x-bind:style="'display: flex'" 
            @endif
            @class([
                'filament-main flex-col gap-y-6 w-screen flex-1 rtl:lg:pl-0',
                'hidden h-full transition-all' => config('filament.layout.sidebar.is_collapsible_on_desktop'),
                'flex lg:pl-[var(--sidebar-width)] rtl:lg:pr-[var(--sidebar-width)] rtl:lg:pl-0' => ! config('filament.layout.sidebar.is_collapsible_on_desktop'),
            ])
        >
            <x-filament::topbar :breadcrumbs="$breadcrumbs" />

            <div @class([
                'filament-main-content flex-1 w-full px-4 mx-auto md:px-6 lg:px-8',
                match ($maxContentWidth ?? config('filament.layout.max_content_width')) {
                    'xl' => 'max-w-xl',
                    '2xl' => 'max-w-2xl',
                    '3xl' => 'max-w-3xl',
                    '4xl' => 'max-w-4xl',
                    '5xl' => 'max-w-5xl',
                    '6xl' => 'max-w-6xl',
                    '7xl' => 'max-w-7xl',
                    null, '8xl' => 'max-w-8xl',
                    'full' => '',
                    default => $maxContentWidth,
                },
            ])>
                {{ \Filament\Facades\Filament::renderHook('content.start') }}

                {{ $slot }}

                {{ \Filament\Facades\Filament::renderHook('content.end') }}
            </div>

            <div class="filament-main-footer py-4 shrink-0">
                <x-filament::footer />
            </div>
        </div>
    </div>

    <style>
        /* Custom CSS for Filament */
        .total-pengajuan-widget {
            grid-column: 1 / -1 !important;
            width: 100% !important;
        }
    </style>
</x-filament::layouts.base>
