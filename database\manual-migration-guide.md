# Panduan Migrasi Manual dari SQLite ke MySQL

Berikut adalah langkah-langkah untuk melakukan migrasi manual dari SQLite ke MySQL:

## Langkah 1: Instal MySQL

1. Unduh dan instal MySQL dari situs resmi MySQL: https://dev.mysql.com/downloads/mysql/
   - Atau gunakan XAMPP/WAMP yang sudah termasuk MySQL

2. Pastikan MySQL server berjalan

## Langkah 2: Buat Database MySQL

1. Buka phpMyAdmin (jika menggunakan XAMPP/WAMP) atau MySQL Workbench
2. Buat database baru dengan nama `pln_sppd`
3. Pastika<PERSON> karakter set adalah `utf8mb4` dan collation adalah `utf8mb4_unicode_ci`

## Langkah 3: Konfigu<PERSON>i <PERSON> untuk MySQL

1. Edit file `.env` dan ubah konfigurasi database:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pln_sppd
DB_USERNAME=root
DB_PASSWORD=
```

2. <PERSON><PERSON><PERSON><PERSON> `DB_USERNAME` dan `DB_PASSWORD` dengan kredensial MySQL Anda

## Langkah 4: Jalankan Migrasi untuk Membuat Skema

1. Buka terminal dan jalankan perintah:

```
php artisan migrate:fresh
```

2. Ini akan membuat semua tabel yang diperlukan di database MySQL

## Langkah 5: Ekspor Data dari SQLite

1. Buat backup database SQLite:

```
copy database\database.sqlite database\database.sqlite.backup
```

2. Gunakan salah satu metode berikut untuk mengekspor data:

### Metode A: Ekspor Manual

1. Gunakan SQLite Browser atau tool lain untuk membuka database SQLite
2. Ekspor setiap tabel ke format CSV
3. Import CSV ke MySQL menggunakan phpMyAdmin atau MySQL Workbench

### Metode B: Gunakan Script PHP

1. Gunakan script `database\migrate-to-mysql.php` yang telah dibuat:

```
php database\migrate-to-mysql.php
```

2. Script ini akan mencoba mengekspor data dari SQLite dan mengimpornya ke MySQL

### Metode C: Gunakan Command Laravel

1. Jalankan command Laravel yang telah dibuat:

```
php artisan db:migrate-to-mysql
```

2. Command ini akan mencoba melakukan migrasi data secara otomatis

## Langkah 6: Verifikasi Migrasi

1. Periksa database MySQL untuk memastikan semua data telah berhasil dimigrasi
2. Jalankan aplikasi dan pastikan semua fitur berfungsi dengan baik

## Catatan Penting

- Pastikan untuk membuat backup database SQLite sebelum melakukan migrasi
- Jika mengalami masalah dengan koneksi MySQL, pastikan server MySQL berjalan dan kredensial yang digunakan benar
- Jika mengalami masalah dengan migrasi otomatis, coba gunakan metode ekspor manual

## Keuntungan Menggunakan MySQL

- Konkurensi yang lebih baik untuk banyak pengguna
- Performa yang lebih baik untuk dataset besar
- Fitur database lanjutan seperti stored procedures dan triggers
- Sistem keamanan dan manajemen pengguna yang lebih lengkap
- Dukungan industri yang lebih luas
