<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Hapus data yang sudah ada untuk menghindari konflik
        DB::statement('PRAGMA foreign_keys = OFF');
        User::truncate();
        DB::table('daftarjabatan')->truncate();
        DB::table('daftargrade')->truncate();
        DB::table('daftarunit')->truncate();
        DB::table('daftarpegawai')->truncate();
        DB::table('daftarpengajuan')->truncate();
        DB::table('totaluangpengajuan')->truncate();
        DB::statement('PRAGMA foreign_keys = ON');

        // Jalankan seeder untuk data master
        $this->call(MasterDataSeeder::class);

        // Jalankan seeder untuk pegawai dummy dan pengajuan SPPD
        $this->call(DummyPegawaiSeeder::class);

        // Jalankan seeder untuk user admin
        $this->call(AdminUserSeeder::class);

        // Jalankan seeder untuk data chat dummy
        $this->call(ChatSeeder::class);
    }
}
