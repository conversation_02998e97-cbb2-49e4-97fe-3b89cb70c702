<?php

namespace App\Providers;

use Filament\Facades\Filament;
use Illuminate\Support\ServiceProvider;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Mengganti metode getUserAvatarUrl untuk menggunakan foto profil dari tabel pegawai
        Filament::serving(function () {
            // Override metode getUserAvatarUrl
            Filament::getUserAvatarUrlUsing(function ($user) {
                // Jika user memiliki relasi pegawai dan pegawai memiliki foto
                if ($user->pegawai && $user->pegawai->foto) {
                    return $user->pegawai->foto;
                }

                // Jika tidak, gunakan foto default
                return url('storage/profile-photos/user.jpg');
            });
        });
    }
}
