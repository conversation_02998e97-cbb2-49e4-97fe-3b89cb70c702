<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat role admin jika belum ada
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        
        // Buat user admin
        $admin = User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'username' => 'admin',
            'password' => Hash::make('admin123'),
            'pegawai_id' => null, // Admin tidak terkait dengan pegawai
        ]);
        
        // Berikan role admin ke user admin
        $admin->assignRole($adminRole);
        
        $this->command->info('Admin user created successfully.');
    }
}
