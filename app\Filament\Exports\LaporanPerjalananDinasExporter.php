<?php

namespace App\Filament\Exports;

use App\Models\Daftarpengajuan;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Filament\Forms;
use Illuminate\Database\Eloquent\Builder;

class LaporanPerjalananDinasExporter extends Exporter
{
    protected static ?string $model = Daftarpengajuan::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('pegawai.pernr')
                ->label('PERNR'),
            ExportColumn::make('pegawai.nama')
                ->label('NAMA'),
            ExportColumn::make('pegawai.nip')
                ->label('NIP'),
            ExportColumn::make('pegawai.grade.namagrade')
                ->label('GRADE'),
            ExportColumn::make('pegawai.jabatan.nama_jabatan')
                ->label('JABATAN'),
            ExportColumn::make('kotatujuan')
                ->label('KOTA TUJUAN'),
            ExportColumn::make('tanggalberangkat')
                ->label('TANGGAL BERANGKAT')
                ->formatStateUsing(fn ($state) => $state ? date('d/m/Y', strtotime($state)) : ''),
            ExportColumn::make('tanggalkembali')
                ->label('TANGGAL KEMBALI')
                ->formatStateUsing(fn ($state) => $state ? date('d/m/Y', strtotime($state)) : ''),
            ExportColumn::make('jenisperjalanan')
                ->label('JENIS PERJALANAN DINAS'),
            ExportColumn::make('tujuanperjalanan')
                ->label('TUJUAN PERJALANAN DINAS'),
            ExportColumn::make('jenistransport')
                ->label('JENIS TRANSPORTASI'),
            ExportColumn::make('keteranganperjalanan')
                ->label('KETERANGAN PERJALANAN DINAS'),
            ExportColumn::make('totaluang.nilaitiket')
                ->label('NILAI TIKET')
                ->formatStateUsing(fn ($state) => $state ? 'Rp ' . number_format($state, 0, ',', '.') : 'Rp 0'),
            ExportColumn::make('hotel')
                ->label('NILAI HOTEL')
                ->formatStateUsing(fn ($state) => $state ? 'Rp ' . number_format($state, 0, ',', '.') : 'Rp 0'),
            ExportColumn::make('status')
                ->label('STATUS'),
            ExportColumn::make('ket')
                ->label('KET')
                ->state(''),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Laporan perjalanan dinas Anda telah selesai dan ' . number_format($export->successful_rows) . ' ' . str('baris')->plural($export->successful_rows) . ' berhasil diekspor.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('baris')->plural($failedRowsCount) . ' gagal diekspor.';
        }

        return $body;
    }

    public static function getFormSchema(): array
    {
        return [
            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\Select::make('status')
                        ->label('Status')
                        ->options([
                            'PENDING' => 'Pending',
                            'DISETUJUI' => 'Disetujui',
                            'DITOLAK' => 'Ditolak',
                            'SELESAI' => 'Selesai',
                        ])
                        ->placeholder('Semua Status')
                        ->native(false),

                    Forms\Components\Select::make('jenisperjalanan')
                        ->label('Jenis Perjalanan')
                        ->options([
                            'PERJALANAN DINAS DIKLAT' => 'Diklat',
                            'PERJALANAN DINAS NON DIKLAT' => 'Non Diklat',
                            'PERJALANAN DINAS MUTASI JABATAN' => 'Mutasi Jabatan',
                            'PERJALANAN DINAS KESEHATAN' => 'Kesehatan',
                        ])
                        ->placeholder('Semua Jenis')
                        ->native(false),
                ]),

            Forms\Components\Grid::make()
                ->schema([
                    Forms\Components\DatePicker::make('tanggal_dari')
                        ->label('Tanggal Berangkat Dari')
                        ->native(false)
                        ->displayFormat('d/m/Y'),

                    Forms\Components\DatePicker::make('tanggal_sampai')
                        ->label('Tanggal Berangkat Sampai')
                        ->native(false)
                        ->displayFormat('d/m/Y'),
                ]),
        ];
    }

    protected function getQuery(): Builder
    {
        $query = static::getModel()::query()
            ->with(['pegawai.grade', 'pegawai.jabatan', 'totaluang']);

        // Filter berdasarkan status jika dipilih
        if ($this->options['status'] ?? null) {
            $query->where('status', $this->options['status']);
        }

        // Filter berdasarkan jenis perjalanan jika dipilih
        if ($this->options['jenisperjalanan'] ?? null) {
            $query->where('jenisperjalanan', $this->options['jenisperjalanan']);
        }

        // Filter berdasarkan tanggal berangkat
        if ($this->options['tanggal_dari'] ?? null) {
            $query->whereDate('tanggalberangkat', '>=', $this->options['tanggal_dari']);
        }

        if ($this->options['tanggal_sampai'] ?? null) {
            $query->whereDate('tanggalberangkat', '<=', $this->options['tanggal_sampai']);
        }

        return $query;
    }
}
