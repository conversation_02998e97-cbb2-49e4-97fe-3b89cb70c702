<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarfasiltasResource\Pages;
use App\Filament\Resources\DaftarfasiltasResource\RelationManagers;
use App\Models\Daftarfasiltas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput\Mask;
use Filament\Forms\Components\TextInput\Mask\PatternBlock;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DaftarfasiltasResource extends Resource
{
    protected static ?string $model = Daftarfasiltas::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $label = 'Fasilitas';
    protected static ?string $pluralLabel = 'Fasilitas';
    protected static ?string $slug = 'fasilitas';
    protected static ?string $navigationLabel = 'Fasilitas';
    protected static ?int $navigationSort = 4;
    protected static ?string $recordTitleAttribute = 'JenjangJabatan';
    protected static ?string $modelLabel = 'Fasilitas';
    protected static ?string $pluralModelLabel = 'Fasilitas';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Fieldset::make('Fasilitas')
                    ->schema([
                        Forms\Components\TextInput::make('JenjangJabatan')
                            ->required()
                            ->label('Jenjang Jabatan'),
                        Forms\Components\TextInput::make('Konsumsi')
                        ->required()
                            ->label('Konsumsi')
                            ->numeric()
                            ->step(100),
                        Forms\Components\TextInput::make('Pakaian')
                            ->required()
                            ->label('Pakaian'),
                        Forms\Components\TextInput::make('Transportlokal')
                            ->required()
                            ->label('Transport Lokal'),
                        Forms\Components\Select::make('GRADE_ID')
                            ->relationship('daftarGrade', 'namagrade')
                            ->preload()
                            ->searchable()
                            ->label('Grade ID'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->label('ID')->sortable(),
                Tables\Columns\TextColumn::make('JenjangJabatan')->label('Jenjang Jabatan')->searchable(),
                Tables\Columns\TextColumn::make('Konsumsi')
                    ->label('Konsumsi')
                    ->formatStateUsing(fn (string $state): string => 'Rp ' . number_format($state, 0, ',', '.')),
                Tables\Columns\TextColumn::make('Pakaian')
                ->label('Pakaian')
                ->formatStateUsing(fn (string $state): string => 'Rp ' . number_format($state, 0, ',', '.')),
                Tables\Columns\TextColumn::make('Transportlokal')
                ->label('Transport Lokal')
                ->formatStateUsing(fn (string $state): string => 'Rp ' . number_format($state, 0, ',', '.')),
                Tables\Columns\TextColumn::make('daftarGrade.namagrade')
                ->label('Grade'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDaftarfasiltas::route('/'),
        ];
    }
}
