<?php

namespace App\Filament\Resources\DaftarpengajuanResource\Pages;

use App\Filament\Resources\DaftarpengajuanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Filament\Forms\Form;
use Asmit\FilamentUpload\Forms\Components\AdvancedFileUpload;

class UploadDokumenPengajuan extends EditRecord
{
    protected static string $resource = DaftarpengajuanResource::class;

    public function getTitle(): string
    {
        return 'Upload Dokumen Pendukung';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Pengajuan')
                    ->description('Detail pengajuan perjalanan dinas')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        Forms\Components\Placeholder::make('pegawai_nama')
                            ->label('Nama Pegawai')
                            ->content(fn ($record) => $record->pegawai->nama ?? '-'),
                        Forms\Components\Placeholder::make('unit_nama')
                            ->label('Unit')
                            ->content(fn ($record) => $record->unit->nama_unit ?? '-'),
                        Forms\Components\Placeholder::make('jenis_perjalanan')
                            ->label('Jenis Perjalanan Dinas')
                            ->content(fn ($record) => $record->jenisperjalanan ?? '-'),
                        Forms\Components\Placeholder::make('kota_tujuan')
                            ->label('Kota Tujuan')
                            ->content(fn ($record) => $record->kotatujuan ?? '-'),
                        Forms\Components\Placeholder::make('tanggal_berangkat')
                            ->label('Tanggal Berangkat')
                            ->content(fn ($record) => $record->tanggalberangkat ?? '-'),
                        Forms\Components\Placeholder::make('tanggal_kembali')
                            ->label('Tanggal Kembali')
                            ->content(fn ($record) => $record->tanggalkembali ?? '-'),
                        Forms\Components\Placeholder::make('status_pengajuan')
                            ->label('Status')
                            ->content(fn ($record) => $record->status ?? '-'),
                    ])
                    ->columns(2)
                    ->collapsible(),
                Forms\Components\Section::make('Dokumen Pendukung')
                    ->description('Upload dokumen pendukung perjalanan dinas')
                    ->icon('heroicon-o-document')
                    ->collapsible()
                    ->schema([
                        AdvancedFileUpload::make('formpermohonan')
                            ->label('Form Permohonan')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120) // 5MB
                            ->downloadable()
                            ->openable()
                            ->previewable()
                            ->pdfPreviewHeight(400)
                            ->pdfDisplayPage(1)
                            ->pdfToolbar(true)
                            ->pdfZoomLevel(100)
                            ->pdfNavPanes(true),

                        AdvancedFileUpload::make('laporanperjalanan')
                            ->label('Laporan Perjalanan Dinas')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable()
                            ->pdfPreviewHeight(400)
                            ->pdfDisplayPage(1)
                            ->pdfToolbar(true)
                            ->pdfZoomLevel(100)
                            ->pdfNavPanes(true),

                        AdvancedFileUpload::make('suratperintah')
                            ->label('Surat Perintah Tugas')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable()
                            ->pdfPreviewHeight(400)
                            ->pdfDisplayPage(1)
                            ->pdfToolbar(true)
                            ->pdfZoomLevel(100)
                            ->pdfNavPanes(true),

                        AdvancedFileUpload::make('undangan')
                            ->label('Undangan')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable()
                            ->pdfPreviewHeight(400)
                            ->pdfDisplayPage(1)
                            ->pdfToolbar(true)
                            ->pdfZoomLevel(100)
                            ->pdfNavPanes(true),

                        Forms\Components\FileUpload::make('invoicehotel')
                            ->label('Invoice Hotel')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable(),

                        Forms\Components\FileUpload::make('invoicetiket')
                            ->label('Invoice Tiket')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable(),

                        Forms\Components\FileUpload::make('boardingpass')
                            ->label('Boarding Pass')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable(),

                        Forms\Components\FileUpload::make('justifikasi')
                            ->label('Justifikasi')
                            ->disk('public')
                            ->directory('dokumen-sppd')
                            ->visibility('public')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable()
                            ->previewable(),
                    ])
                    ->columns(2),
            ]);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Dokumen pendukung berhasil diupload';
    }
}
