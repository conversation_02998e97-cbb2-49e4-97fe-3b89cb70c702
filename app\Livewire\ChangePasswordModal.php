<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password as PasswordRule;
use Illuminate\Validation\ValidationException;
use Livewire\Component;
use Filament\Notifications\Notification;

class ChangePasswordModal extends Component
{
    public bool $isOpen = false;
    public string $current_password = '';
    public string $password = '';
    public string $password_confirmation = '';

    protected $listeners = ['openChangePasswordModal' => 'open'];

    public function open()
    {
        $this->isOpen = true;
    }

    public function close()
    {
        $this->isOpen = false;
        $this->reset('current_password', 'password', 'password_confirmation');
        $this->resetValidation();
    }

    public function updatePassword(): void
    {
        try {
            $validated = $this->validate([
                'current_password' => ['required', 'string', 'current_password'],
                'password' => ['required', 'string', PasswordRule::defaults(), 'confirmed'],
            ]);
        } catch (ValidationException $e) {
            $this->reset('current_password', 'password', 'password_confirmation');
            throw $e;
        }

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        $this->reset('current_password', 'password', 'password_confirmation');
        $this->close();

        Notification::make()
            ->title('Password berhasil diubah')
            ->success()
            ->send();
    }

    public function render()
    {
        return view('livewire.change-password-modal');
    }
}
