<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpagu;
use App\Models\Daftarpengajuan;
use App\Models\DaftarUnit;
use Filament\Forms\Components\Select;
use Filament\Widgets\ChartWidget;

class UnitFilteredSPPD<PERSON>hart extends ChartWidget
{
    protected static ?string $heading = '<PERSON><PERSON>gu dan Total SPPD per Unit';
    protected static ?int $sort = 4; // Tampilkan setelah widget PaguVsTotalSPPDChart
    protected int | string | array $columnSpan = 6; // Setengah layar (dari 12 kolom)

    // Filter unit
    public ?string $unitId = null;

    protected function getFormSchema(): array
    {
        return [
            Select::make('unitId')
                ->label('Filter Unit')
                ->options(DaftarUnit::pluck('nama_unit', 'id'))
                ->placeholder('Semua Unit')
                ->live()
                ->afterStateUpdated(function () {
                    $this->updateChartData();
                }),
        ];
    }
    
    // Fungsi untuk memperbarui data grafik
    public function updateChartData(): void
    {
        $this->updateChart();
    }

    protected function getData(): array
    {
        // Query dasar untuk mendapatkan data unit
        $unitQuery = DaftarUnit::query();

        // Jika ada filter unit, terapkan filter
        if ($this->unitId) {
            $unitQuery->where('id', $this->unitId);
        }

        // Ambil data unit
        $units = $unitQuery->get();

        // Siapkan array untuk data dan label
        $paguValues = [];
        $sppdValues = [];
        $labels = [];

        // Untuk setiap unit, ambil nilai pagu dan total SPPD
        foreach ($units as $unit) {
            // Tambahkan nama unit ke labels
            $labels[] = $unit->nama_unit;

            // Ambil nilai pagu untuk unit ini
            $pagu = Daftarpagu::where('daftarunit_id', $unit->id)->value('jumlahpagu') ?? 0;
            $paguValues[] = $pagu;

            // Ambil total nilai SPPD untuk unit ini
            $totalSPPD = Daftarpengajuan::where('UNIT_ID', $unit->id)
                ->join('totaluangpengajuan', 'daftarpengajuan.id', '=', 'totaluangpengajuan.pengajuan_id')
                ->sum('totaluangpengajuan.totalnilai') ?? 0;
            $sppdValues[] = $totalSPPD;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Nilai Pagu',
                    'data' => $paguValues,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.5)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1
                ],
                [
                    'label' => 'Total SPPD',
                    'data' => $sppdValues,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.5)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 1
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + new Intl.NumberFormat("id-ID").format(value); }',
                    ],
                ],
            ],
            'plugins' => [
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { 
                            var label = context.dataset.label || "";
                            if (label) {
                                label += ": ";
                            }
                            label += "Rp " + new Intl.NumberFormat("id-ID").format(context.raw);
                            return label;
                        }',
                    ],
                ],
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'datalabels' => [
                    'color' => '#000',
                    'font' => [
                        'weight' => 'bold',
                    ],
                    'formatter' => 'function(value) { 
                        if (value > 1000000) {
                            return "Rp " + (value / 1000000).toFixed(1) + " Juta"; 
                        }
                        return "Rp " + new Intl.NumberFormat("id-ID").format(value);
                    }',
                    'anchor' => 'end',
                    'align' => 'top',
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
