<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class StartQueueWorker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:start-queue-worker';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start queue worker in background';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting queue worker in background...');
        
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows implementation
            $process = new Process(['start', 'cmd', '/c', 'php', 'artisan', 'queue:work', 'database', '--sleep=3', '--tries=3']);
            $process->setWorkingDirectory(base_path());
            $process->disableOutput();
            $process->start();
            
            $this->info('Queue worker started in background. Do not close the command window that opens.');
        } else {
            // Linux/macOS implementation
            $process = new Process(['nohup', 'php', 'artisan', 'queue:work', 'database', '--sleep=3', '--tries=3', '>', 'storage/logs/worker.log', '2>&1', '&']);
            $process->setWorkingDirectory(base_path());
            $process->disableOutput();
            $process->start();
            
            $this->info('Queue worker started in background. Check storage/logs/worker.log for output.');
        }
        
        return Command::SUCCESS;
    }
}
