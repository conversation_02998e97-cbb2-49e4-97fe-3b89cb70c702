<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Daftarjabatan;
use App\Models\Daftargrade;
use App\Models\Daftarunit;

class MasterDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed jabatan
        $jabatan = [
            ['nama_jabatan' => 'Manager'],
            ['nama_jabatan' => 'Staff'],
            ['nama_jabatan' => 'Supervisor'],
            ['nama_jabatan' => 'Director'],
            ['nama_jabatan' => 'Assistant Manager'],
        ];

        foreach ($jabatan as $data) {
            Daftarjabatan::create($data);
        }

        // Seed grade
        $grade = [
            ['namagrade' => 'Grade 1'],
            ['namagrade' => 'Grade 2'],
            ['namagrade' => 'Grade 3'],
            ['namagrade' => 'Grade 4'],
            ['namagrade' => 'Grade 5'],
        ];

        foreach ($grade as $data) {
            Daftargrade::create($data);
        }

        // Seed unit
        $unit = [
            ['nama_unit' => 'IT'],
            ['nama_unit' => 'HR'],
            ['nama_unit' => 'Finance'],
            ['nama_unit' => 'Marketing'],
            ['nama_unit' => 'Operations'],
        ];

        foreach ($unit as $data) {
            Daftarunit::create($data);
        }
    }
}
