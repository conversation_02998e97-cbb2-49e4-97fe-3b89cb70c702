<?php

namespace App\Filament\Widgets;

use App\Models\Daftarpengajuan;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

// Nonaktifkan widget ini karena digantikan oleh FullWidthTotalPengajuanWidget
class TotalPengajuanWidget_disabled extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';
    protected static ?int $sort = 1; // Tampilkan pertama
    protected static bool $isLazy = false;

    protected function getColumns(): int
    {
        // Menggunakan 4 kolom agar memanjang dari SPPD Close sampai SPPD Ditolak
        return 4;
    }

    protected function getStats(): array
    {
        // Hitung total pengajuan
        $totalPengajuan = Daftarpengajuan::count();

        // Hitung persentase perubahan dari minggu lalu
        $lastWeek = Carbon::now()->subWeek();
        $totalLastWeek = Daftarpengajuan::where('created_at', '<', $lastWeek)->count();
        $totalChange = $totalLastWeek > 0 ? (($totalPengajuan - $totalLastWeek) / $totalLastWeek) * 100 : 100;

        // Dapatkan data untuk chart (7 hari terakhir)
        $totalChart = $this->getChartData('all');

        return [
            Stat::make('Total Pengajuan SPPD', $totalPengajuan)
                ->description(sprintf('%+.1f%% dari minggu lalu', $totalChange))
                ->descriptionIcon($totalChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color('gray')
                ->chart($totalChart)
                ->extraAttributes([
                    'class' => 'ring-2 ring-gray-500/30 bg-gray-500/10 dark:ring-gray-500/20 dark:bg-gray-500/5 total-pengajuan-widget',
                    'style' => 'background-color: rgba(220, 220, 220, 0.3); border-color: rgba(180, 180, 180, 0.5); grid-column: span 4 / span 4;',
                ]),
        ];
    }

    protected function getChartData(string $status): array
    {
        $data = [];

        // Dapatkan data untuk 7 hari terakhir
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $startOfDay = Carbon::parse($date)->startOfDay();
            $endOfDay = Carbon::parse($date)->endOfDay();

            if ($status === 'all') {
                $count = Daftarpengajuan::whereBetween('created_at', [$startOfDay, $endOfDay])->count();
            } else {
                $count = Daftarpengajuan::where('status', $status)
                    ->whereBetween('updated_at', [$startOfDay, $endOfDay])
                    ->count();
            }

            $data[] = $count;
        }

        return $data;
    }
}
