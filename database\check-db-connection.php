<?php

/**
 * Script untuk memeriksa koneksi database yang digunakan oleh aplikasi
 */

// Ambil konfigurasi dari .env
$envContent = file_get_contents('.env');
preg_match('/DB_CONNECTION=([^\n]+)/', $envContent, $matches);
$connection = $matches[1] ?? 'unknown';

echo "Koneksi database yang digunakan: {$connection}\n";

// Cek apakah menggunakan MySQL
if ($connection === 'mysql') {
    preg_match('/DB_HOST=([^\n]+)/', $envContent, $matches);
    $host = $matches[1] ?? '127.0.0.1';
    
    preg_match('/DB_PORT=([^\n]+)/', $envContent, $matches);
    $port = $matches[1] ?? '3306';
    
    preg_match('/DB_DATABASE=([^\n]+)/', $envContent, $matches);
    $database = $matches[1] ?? 'pln_sppd';
    
    preg_match('/DB_USERNAME=([^\n]+)/', $envContent, $matches);
    $username = $matches[1] ?? 'root';
    
    preg_match('/DB_PASSWORD=([^\n]+)/', $envContent, $matches);
    $password = $matches[1] ?? '';
    
    echo "Host: {$host}\n";
    echo "Port: {$port}\n";
    echo "Database: {$database}\n";
    echo "Username: {$username}\n";
    echo "Password: " . (empty($password) ? '(empty)' : '(set)') . "\n";
    
    try {
        // Koneksi ke MySQL
        $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Dapatkan daftar tabel
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "\nTidak ada tabel di database {$database}.\n";
        } else {
            echo "\nDaftar tabel di database {$database}:\n";
            foreach ($tables as $table) {
                echo "- {$table}\n";
                
                // Dapatkan jumlah baris
                $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
                $count = $stmt->fetchColumn();
                echo "  Jumlah baris: {$count}\n";
            }
        }
        
        echo "\nKoneksi ke MySQL berhasil.\n";
    } catch (PDOException $e) {
        die("\nKoneksi ke MySQL gagal: " . $e->getMessage() . "\n");
    }
} else if ($connection === 'sqlite') {
    echo "Database SQLite digunakan.\n";
    
    try {
        // Koneksi ke SQLite
        $pdo = new PDO("sqlite:" . __DIR__ . "/database.sqlite");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Dapatkan daftar tabel
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "\nTidak ada tabel di database SQLite.\n";
        } else {
            echo "\nDaftar tabel di database SQLite:\n";
            foreach ($tables as $table) {
                echo "- {$table}\n";
                
                // Dapatkan jumlah baris
                $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
                $count = $stmt->fetchColumn();
                echo "  Jumlah baris: {$count}\n";
            }
        }
        
        echo "\nKoneksi ke SQLite berhasil.\n";
    } catch (PDOException $e) {
        die("\nKoneksi ke SQLite gagal: " . $e->getMessage() . "\n");
    }
} else {
    echo "Koneksi database tidak dikenali.\n";
}
