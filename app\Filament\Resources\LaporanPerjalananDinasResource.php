<?php

namespace App\Filament\Resources;

use App\Filament\Exports\LaporanPerjalananDinasExporter;
use App\Filament\Resources\LaporanPerjalananDinasResource\Pages;
use App\Models\Daftarpengajuan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Actions\ExportAction;

class LaporanPerjalananDinasResource extends Resource
{
    protected static ?string $model = Daftarpengajuan::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Laporan Perjalanan Dinas';
    protected static ?string $label = 'Laporan Perjalanan Dinas';
    protected static ?string $pluralLabel = 'Laporan Perjalanan Dinas';
    protected static ?string $slug = 'laporan-perjalanan-dinas';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak digunakan untuk resource ini
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with(['pegawai.grade', 'pegawai.jabatan', 'totaluang']);

        // Jika user bukan admin, hanya tampilkan pengajuan miliknya
        $user = Auth::user();
        if ($user && $user->pegawai_id) {
            // Cek apakah user memiliki role admin
            if (!$user->hasAnyRole(['admin', 'super_admin'])) {
                $query->where('PEGAWAI_ID', $user->pegawai_id);
            }
        }

        return $query;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(static::getEloquentQuery())
            ->defaultSort('tanggalberangkat', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('pegawai.pernr')
                    ->label('PERNR')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pegawai.nama')
                    ->label('NAMA')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pegawai.nip')
                    ->label('NIP')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pegawai.grade.namagrade')
                    ->label('GRADE')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pegawai.jabatan.nama_jabatan')
                    ->label('JABATAN')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('kotatujuan')
                    ->label('KOTA TUJUAN')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tanggalberangkat')
                    ->label('TANGGAL BERANGKAT')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('tanggalkembali')
                    ->label('TANGGAL KEMBALI')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('jenisperjalanan')
                    ->label('JENIS PERJALANAN')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tujuanperjalanan')
                    ->label('TUJUAN PERJALANAN')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('jenistransport')
                    ->label('JENIS TRANSPORTASI')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('keteranganperjalanan')
                    ->label('KETERANGAN PERJALANAN')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('totaluang.nilaitiket')
                    ->label('NILAI TIKET')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('hotel')
                    ->label('NILAI HOTEL')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('STATUS')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'PENDING' => 'warning',
                        'DISETUJUI' => 'info',
                        'DITOLAK' => 'danger',
                        'SELESAI' => 'success',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ket')
                    ->label('KET')
                    ->placeholder('-')
                    ->sortable(),
            ])
            ->filters([
                Filter::make('tanggal')
                    ->form([
                        Forms\Components\DatePicker::make('tanggal_dari')
                            ->label('Tanggal Berangkat Dari'),
                        Forms\Components\DatePicker::make('tanggal_sampai')
                            ->label('Tanggal Berangkat Sampai'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['tanggal_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggalberangkat', '>=', $date),
                            )
                            ->when(
                                $data['tanggal_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggalberangkat', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['tanggal_dari'] ?? null) {
                            $indicators['tanggal_dari'] = 'Dari: ' . Carbon::parse($data['tanggal_dari'])->format('d/m/Y');
                        }

                        if ($data['tanggal_sampai'] ?? null) {
                            $indicators['tanggal_sampai'] = 'Sampai: ' . Carbon::parse($data['tanggal_sampai'])->format('d/m/Y');
                        }

                        return $indicators;
                    }),

                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'PENDING' => 'Pending',
                        'DISETUJUI' => 'Disetujui',
                        'DITOLAK' => 'Ditolak',
                        'SELESAI' => 'Selesai',
                    ])
                    ->placeholder('Semua Status'),

                SelectFilter::make('jenisperjalanan')
                    ->label('Jenis Perjalanan')
                    ->options([
                        'PERJALANAN DINAS DIKLAT' => 'Diklat',
                        'PERJALANAN DINAS NON DIKLAT' => 'Non Diklat',
                        'PERJALANAN DINAS MUTASI JABATAN' => 'Mutasi Jabatan',
                        'PERJALANAN DINAS KESEHATAN' => 'Kesehatan',
                    ])
                    ->placeholder('Semua Jenis'),
            ])
            ->actions([
                // Tidak ada aksi per baris
            ])
            ->bulkActions([
                // Tidak ada bulk actions
            ])
            ->headerActions([
                ExportAction::make()
                    ->label('Cetak Laporan')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->exporter(LaporanPerjalananDinasExporter::class)
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLaporanPerjalananDinas::route('/'),
        ];
    }
}

