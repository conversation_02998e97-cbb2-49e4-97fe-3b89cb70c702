<?php

namespace App\Filament\Pages;

use App\Models\Chat;
use App\Models\Daftarpengajuan;
use App\Models\User;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class UserChat extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationLabel = 'Pesan';
    protected static ?string $title = 'Pesan';
    protected static ?string $navigationGroup = 'Komunikasi';
    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.user-chat';

    public ?array $data = [];
    public $selectedUser = null;
    public $messages = [];
    public $unreadCount = 0;
    public $searchTerm = '';

    public function mount(): void
    {
        $this->form->fill();
        $this->refreshMessages();
        $this->refreshUnreadCount();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('receiver_id')
                    ->label('Kirim Pesan Ke')
                    ->options(function () {
                        // Ambil semua admin
                        return User::role('admin')->pluck('name', 'id');
                    })
                    ->searchable()
                    ->required(),
                Select::make('pengajuan_id')
                    ->label('Terkait Pengajuan (Opsional)')
                    ->options(function () {
                        // Ambil pengajuan milik user yang login
                        return Daftarpengajuan::where('pegawai_id', Auth::user()->pegawai->id ?? 0)
                            ->get()
                            ->mapWithKeys(function ($item) {
                                return [$item->id => "ID: {$item->id} - {$item->kotatujuan} ({$item->tanggalberangkat})"];
                            });
                    })
                    ->searchable()
                    ->placeholder('Pilih pengajuan terkait (opsional)')
                    ->nullable(),
                Textarea::make('message')
                    ->label(false)
                    ->placeholder('Tulis pesan...')
                    ->required()
                    ->rows(2)
                    ->extraInputAttributes(['class' => 'rounded-full']),
            ])
            ->statePath('data');
    }

    public function sendMessage()
    {
        $data = $this->form->getState();

        $chat = new Chat();
        $chat->sender_id = Auth::id();
        $chat->receiver_id = $data['receiver_id'];
        $chat->pengajuan_id = $data['pengajuan_id'] ?? null;
        $chat->message = $data['message'];
        $chat->is_read = false;
        $chat->save();

        $this->form->fill([
            'receiver_id' => $data['receiver_id'],
            'pengajuan_id' => $data['pengajuan_id'],
            'message' => '',
        ]);

        $this->refreshMessages();

        Notification::make()
            ->title('Pesan terkirim')
            ->success()
            ->send();
    }

    public function selectUser($userId)
    {
        $this->selectedUser = $userId;
        $this->refreshMessages();

        // Mark messages as read
        Chat::where('sender_id', $userId)
            ->where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);

        $this->refreshUnreadCount();
    }

    public function refreshMessages()
    {
        if ($this->selectedUser) {
            $this->messages = Chat::conversation(Auth::id(), $this->selectedUser)
                ->with(['sender', 'receiver', 'pengajuan'])
                ->orderBy('created_at', 'asc')
                ->get();
        } else {
            // Get unique users that have conversations with the current user
            $userIds = Chat::where('sender_id', Auth::id())
                ->orWhere('receiver_id', Auth::id())
                ->get(['sender_id', 'receiver_id'])
                ->flatMap(function ($chat) {
                    return [$chat->sender_id, $chat->receiver_id];
                })
                ->reject(function ($id) {
                    return $id === Auth::id();
                })
                ->unique()
                ->values();

            // Get the latest message for each conversation
            $messages = collect();

            foreach ($userIds as $userId) {
                $user = User::find($userId);

                // Filter users by search term if provided
                if ($this->searchTerm && !str_contains(strtolower($user->name), strtolower($this->searchTerm))) {
                    continue;
                }

                $latestMessage = Chat::conversation(Auth::id(), $userId)
                    ->with(['sender', 'receiver'])
                    ->latest()
                    ->first();

                if ($latestMessage) {
                    $messages->push($latestMessage);
                }
            }

            $this->messages = $messages;
        }
    }

    public function refreshUnreadCount()
    {
        $this->unreadCount = Chat::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->count();
    }

    #[On('refresh-messages')]
    public function handleRefreshMessages()
    {
        $this->refreshMessages();
        $this->refreshUnreadCount();
    }

    public function updatedSearchTerm()
    {
        $this->refreshMessages();
    }
}
