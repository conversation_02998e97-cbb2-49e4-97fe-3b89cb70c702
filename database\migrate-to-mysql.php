<?php

/**
 * Script untuk migrasi data dari SQLite ke MySQL
 * 
 * Cara penggunaan:
 * 1. Pastikan database MySQL sudah dibuat
 * 2. Pastikan konfigurasi .env sudah diubah ke MySQL
 * 3. Jalankan: php database/migrate-to-mysql.php
 */

// Pastikan script dijalankan dari root project
if (!file_exists('artisan')) {
    die("Script harus dijalankan dari root project Laravel\n");
}

echo "Memulai migrasi data dari SQLite ke MySQL...\n";

// Langkah 1: Backup konfigurasi database saat ini
$envContent = file_get_contents('.env');
$sqliteConfig = "DB_CONNECTION=sqlite";
$mysqlConfig = "DB_CONNECTION=mysql";

// Langkah 2: Ubah konfigurasi ke SQLite untuk mengambil data
file_put_contents('.env.mysql', $envContent); // Backup konfigurasi MySQL
$sqliteEnv = str_replace($mysqlConfig, $sqliteConfig, $envContent);
file_put_contents('.env', $sqliteEnv);

echo "Mengekspor data dari SQLite...\n";

// Langkah 3: Dapatkan semua tabel dari database SQLite
exec('php artisan tinker --execute="Schema::getConnection()->getDoctrineSchemaManager()->listTableNames();"', $tables, $returnCode);

if ($returnCode !== 0) {
    die("Gagal mendapatkan daftar tabel dari SQLite\n");
}

// Parse output untuk mendapatkan nama tabel
$tableList = [];
$capture = false;
foreach ($tables as $line) {
    if (strpos($line, '=>') !== false) {
        $capture = true;
    }
    
    if ($capture && strpos($line, ']') !== false) {
        $capture = false;
    }
    
    if ($capture && preg_match('/\[\d+\]\s*=>\s*"([^"]+)"/', $line, $matches)) {
        $tableList[] = $matches[1];
    }
}

// Filter tabel yang tidak perlu dimigrasi
$tableList = array_filter($tableList, function($table) {
    return !in_array($table, [
        'migrations', 
        'password_reset_tokens', 
        'personal_access_tokens',
        'failed_jobs'
    ]);
});

// Langkah 4: Ekspor data dari setiap tabel
$exportData = [];
foreach ($tableList as $table) {
    echo "Mengekspor data dari tabel: $table\n";
    exec('php artisan tinker --execute="DB::table(\'' . $table . '\')->get()->toArray();"', $output, $returnCode);
    
    if ($returnCode !== 0) {
        echo "Peringatan: Gagal mengekspor data dari tabel $table\n";
        continue;
    }
    
    // Simpan output untuk diproses nanti
    $exportData[$table] = $output;
}

// Langkah 5: Ubah konfigurasi kembali ke MySQL
echo "Beralih ke database MySQL...\n";
file_put_contents('.env', file_get_contents('.env.mysql'));
unlink('.env.mysql');

// Langkah 6: Jalankan migrasi untuk membuat skema di MySQL
echo "Menjalankan migrasi untuk membuat skema di MySQL...\n";
exec('php artisan migrate:fresh --force', $output, $returnCode);

if ($returnCode !== 0) {
    die("Gagal menjalankan migrasi di MySQL\n");
}

// Langkah 7: Import data ke MySQL
echo "Mengimpor data ke MySQL...\n";

foreach ($tableList as $table) {
    if (!isset($exportData[$table])) {
        continue;
    }
    
    echo "Mengimpor data ke tabel: $table\n";
    
    // Parse data yang diekspor
    $data = [];
    $json = '';
    $inArray = false;
    
    foreach ($exportData[$table] as $line) {
        if (strpos($line, '=> [') !== false) {
            $inArray = true;
            $json = '';
        }
        
        if ($inArray) {
            $json .= $line . "\n";
        }
        
        if ($inArray && strpos($line, '],') !== false) {
            $inArray = false;
            
            // Ekstrak JSON dari output tinker
            if (preg_match('/\[\s*(.*)\s*\]/', $json, $matches)) {
                $jsonStr = $matches[1];
                // Ubah format Tinker ke JSON valid
                $jsonStr = preg_replace('/"(\w+)" => /', '"$1":', $jsonStr);
                $jsonStr = preg_replace('/(\d+) => /', '', $jsonStr);
                $jsonStr = '{' . $jsonStr . '}';
                
                // Coba parse JSON
                $item = json_decode($jsonStr, true);
                if ($item) {
                    $data[] = $item;
                }
            }
        }
    }
    
    // Import data ke MySQL
    if (!empty($data)) {
        $chunks = array_chunk($data, 100); // Proses dalam chunk untuk menghindari query terlalu besar
        
        foreach ($chunks as $chunk) {
            $values = [];
            $placeholders = [];
            $columns = array_keys($chunk[0]);
            
            foreach ($chunk as $row) {
                $rowPlaceholders = [];
                foreach ($columns as $column) {
                    $values[] = $row[$column] ?? null;
                    $rowPlaceholders[] = '?';
                }
                $placeholders[] = '(' . implode(', ', $rowPlaceholders) . ')';
            }
            
            $sql = "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES " . implode(', ', $placeholders);
            
            exec('php artisan tinker --execute="DB::statement(\'' . addslashes($sql) . '\', ' . json_encode($values) . ');"', $output, $returnCode);
            
            if ($returnCode !== 0) {
                echo "Peringatan: Gagal mengimpor beberapa data ke tabel $table\n";
            }
        }
    }
}

echo "Migrasi data selesai!\n";
echo "Database telah berhasil dimigrasi dari SQLite ke MySQL.\n";
