<?php

namespace App\Http\Controllers;

use App\Models\Daftarpengajuan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LaporanPerjalananDinasPrintController extends Controller
{
    /**
     * Handle the incoming request to print laporan perjalanan dinas.
     */
    public function __invoke(Request $request)
    {
        // Ambil parameter filter dari request
        $status = $request->input('status');
        $jenisPerjalanan = $request->input('jenis_perjalanan');
        $tanggalDari = $request->input('tanggal_dari');
        $tanggalSampai = $request->input('tanggal_sampai');

        // Buat query dasar
        $query = Daftarpengajuan::query()
            ->with(['pegawai.grade', 'pegawai.jabatan', 'totaluang']);

        // Terapkan filter
        if ($status) {
            $query->where('status', $status);
        }

        if ($jenisPerjalanan) {
            $query->where('jeni<PERSON>jalanan', $jenisPerjalanan);
        }

        if ($tanggalDari) {
            $query->whereDate('tanggalberangkat', '>=', $tanggalDari);
        }

        if ($tanggalSampai) {
            $query->whereDate('tanggalberangkat', '<=', $tanggalSampai);
        }

        // Jika user bukan admin, hanya tampilkan pengajuan miliknya
        $user = Auth::user();
        if ($user && $user->pegawai_id) {
            // Cek apakah user memiliki role admin
            $isAdmin = DB::table('model_has_roles')
                ->join('roles', 'roles.id', '=', 'model_has_roles.role_id')
                ->where('model_has_roles.model_id', $user->id)
                ->where('roles.name', 'admin')
                ->exists();

            if (!$isAdmin) {
                $query->where('PEGAWAI_ID', $user->pegawai_id);
            }
        }

        // Ambil data
        $data = $query->orderBy('tanggalberangkat', 'desc')->get();

        // Tampilkan view dengan data
        return view('laporan.print', [
            'data' => $data,
            'tanggalCetak' => now()->format('d/m/Y H:i:s'),
            'user' => $user,
        ]);
    }
}
