<?php

namespace App\Providers;

use App\Models\Daftarpengajuan;
use App\Observers\DaftarpengajuanObserver;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Symfony\Component\Process\Process;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        Daftarpengajuan::observe(DaftarpengajuanObserver::class);

        // Register Livewire components
        Livewire::component('change-password-modal', \App\Livewire\ChangePasswordModal::class);

        // Kode untuk mengganti avatar user dihapus karena metode tidak tersedia di versi Filament ini

        // Start queue worker automatically in local environment
        if (app()->environment('local') && PHP_SAPI !== 'cli') {
            // Cek apakah queue worker sudah berjalan
            $isRunning = false;

            if (PHP_OS_FAMILY === 'Windows') {
                // Windows implementation - check if php artisan queue:work is running
                $process = new Process(['tasklist', '/FI', 'IMAGENAME eq php.exe', '/FO', 'CSV']);
                $process->run();
                $output = $process->getOutput();
                $isRunning = str_contains($output, 'artisan queue:work');
            } else {
                // Linux/macOS implementation - check if php artisan queue:work is running
                $process = new Process(['ps', 'aux']);
                $process->run();
                $output = $process->getOutput();
                $isRunning = str_contains($output, 'artisan queue:work');
            }

            // Jika queue worker belum berjalan, jalankan
            if (!$isRunning) {
                try {
                    Artisan::call('app:start-queue-worker');
                } catch (\Exception $e) {
                    // Log error jika gagal menjalankan queue worker
                    Log::error('Failed to start queue worker: ' . $e->getMessage());
                }
            }
        }
    }
}
