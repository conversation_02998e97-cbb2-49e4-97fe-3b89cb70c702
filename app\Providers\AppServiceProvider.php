<?php

namespace App\Providers;

use App\Models\Daftarpengajuan;
use App\Observers\DaftarpengajuanObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        Daftarpengajuan::observe(DaftarpengajuanObserver::class);

        // Kode untuk mengganti avatar user dihapus karena metode tidak tersedia di versi Filament ini
    }
}
