<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Daftargrade;
use App\Models\Daftarjabatan;
use App\Models\Daftarunit;

// Buat data grade
if (Daftargrade::count() == 0) {
    echo "Membuat data grade...\n";
    $grade = new Daftargrade();
    $grade->namagrade = 'Grade 1';
    $grade->save();

    $grade = new Daftargrade();
    $grade->namagrade = 'Grade 2';
    $grade->save();

    $grade = new Daftargrade();
    $grade->namagrade = 'Grade 3';
    $grade->save();

    echo "Data grade berhasil dibuat.\n";
}

// Buat data jabatan
if (Daftarjabatan::count() == 0) {
    echo "Membuat data jabatan...\n";
    $jabatan = new Daftarjabatan();
    $jabatan->nama_jabatan = 'Manager';
    $jabatan->save();

    $jabatan = new Daftarjabatan();
    $jabatan->nama_jabatan = 'Supervisor';
    $jabatan->save();

    $jabatan = new Daftarjabatan();
    $jabatan->nama_jabatan = 'Staff';
    $jabatan->save();

    echo "Data jabatan berhasil dibuat.\n";
}

// Buat data unit
if (Daftarunit::count() == 0) {
    echo "Membuat data unit...\n";
    $unit = new Daftarunit();
    $unit->nama_unit = 'IT';
    $unit->save();

    $unit = new Daftarunit();
    $unit->nama_unit = 'HR';
    $unit->save();

    $unit = new Daftarunit();
    $unit->nama_unit = 'Finance';
    $unit->save();

    echo "Data unit berhasil dibuat.\n";
}

echo "Selesai membuat data dasar.\n";
