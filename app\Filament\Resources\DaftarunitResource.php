<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DaftarunitResource\Pages;
use App\Filament\Resources\DaftarunitResource\RelationManagers;
use App\Models\Daftarunit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Contracts\HasTable;
use stdClass;

class DaftarunitResource extends Resource
{
    protected static ?string $model = Daftarunit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $navigationLabel = 'Daftar Unit';
    protected static ?string $label = 'Daftar Unit';
    protected static ?string $pluralLabel = 'Daftar Unit';
    protected static ?string $recordTitleAttribute = 'nama_unit';
    protected static ?string $modelLabel = 'Daftar Unit';
    protected static ?string $pluralModelLabel = 'Daftar Unit';
    protected static ?int $navigationSort = 1;
    protected static ?string $searchLabel = 'Pencarian';
    protected static ?string $searchPlaceholder = 'Cari berdasarkan nama unit';
    protected static ?string $searchResultLabel = 'Hasil Pencarian';
    protected static ?string $searchResultPlaceholder = 'Tidak ada hasil pencarian';
    protected static ?string $searchResultEmptyStateHeading = 'Tidak ada hasil pencarian';
    protected static ?string $searchResultEmptyStateDescription = 'Cobalah untuk mencari dengan kata kunci lain.';
    protected static ?string $searchResultEmptyStateActionLabel = 'Buat unit baru';
    protected static ?string $searchResultEmptyStateActionUrl = 'create7';
    protected static ?string $searchResultEmptyStateActionIcon = 'heroicon-o-plus';
    protected static ?string $searchResultEmptyStateActionColor = 'primary';
    protected static ?string $searchResultEmptyStateActionSize = 'md';
    protected static ?string $searchResultEmptyStateActionVariant = 'primary';
    protected static ?string $searchResultEmptyStateActionIconPosition = 'left';
    protected static ?string $searchResultEmptyStateActionIconSize = 'md';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Fieldset::make('Daftar Unit')
                    ->schema([
                        Forms\Components\TextInput::make('nama_unit')
                            ->required()
                            ->label('Nama Unit'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('No.')->state(
                    static function (HasTable $livewire, stdClass $rowLoop): string {
                        return (string) (
                            $rowLoop->iteration +
                            ($livewire->getTableRecordsPerPage() * (
                                $livewire->getTablePage() - 1
                            ))
                        );
                    }
                ),
                Tables\Columns\TextColumn::make('nama_unit')->label('Nama Unit')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageDaftarunits::route('/'),
        ];
    }
}
