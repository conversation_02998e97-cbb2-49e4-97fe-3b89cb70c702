<?php

namespace App\Filament\Widgets;

use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class SimpleBarChart extends ApexChartWidget
{
    /**
     * Chart Id
     */
    protected static ?string $chartId = 'simpleBarChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'Test Grafik Sederhana';

    /**
     * Sort
     */
    protected static ?int $sort = 6;

    /**
     * Widget Height
     */
    protected static ?string $maxHeight = '300px';

    /**
     * Column Span
     */
    protected int | string | array $columnSpan = 'full';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
            ],
            'series' => [
                [
                    'name' => 'Test Data',
                    'data' => [10, 20, 30, 40],
                ],
            ],
            'xaxis' => [
                'categories' => ['A', 'B', 'C', 'D'],
            ],
        ];
    }
}
